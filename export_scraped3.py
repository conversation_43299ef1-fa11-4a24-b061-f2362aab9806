import sys

from gsheet_manager import GSheetManager
import pandas as pd
import logging
from datetime import datetime, date
from collections import defaultdict

logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")

def col_letter_to_index(letter):
    """
    Convert column letter (e.g. 'A') to 1-based index.
    """
    letter = letter.upper().strip()
    index = 0
    for char in letter:
        index = index * 26 + (ord(char) - ord('A') + 1)
    return index

def index_to_col_letter(index):
    """
    Convert 1-based column index (e.g. 1) to letter (e.g. 'A').
    """
    result = []
    while index > 0:
        remainder = (index - 1) % 26
        result.append(chr(remainder + ord('A')))
        index = (index - 1) // 26
    return ''.join(reversed(result))

def parse_scraped_date(date_str):
    """
    'YYYY-MM-DD' -> 'YYYY_MM_DD'
    """
    dt = datetime.strptime(date_str, "%Y-%m-%d")
    return f"{dt.year}_{dt.month:02d}_{dt.day:02d}"

def is_excluded_gift(name: str) -> bool:
    """
    *Dùng cho Shop-level* để loại bỏ item quà tặng (dựa trên tên).
    Ở Item-level => KHÔNG áp dụng (theo yêu cầu).
    """
    txt = name.upper().replace(" ", "")
    # Nếu có 'TẶNGKÈMQUÀ' => không coi là quà tặng
    if "TẶNGKÈMQUÀ" in txt:
        return False
        
    # Nếu có 'QUÀTẶNG', 'HÀNGTẶNG', 'GIFT' => exclude
    if ("QUÀTẶNG" in txt or "HÀNGTẶNG" in txt or "GIFT" in txt or 
        "QT" in txt or "QÙATẶNG" in txt):
        return True
        
    # Xử lý text có dấu đặc biệt như "Q.U,A T,A.N:G K:H;Ô!N?G B@N"
    import re
    clean_txt = re.sub(r'[^A-ZÀ-Ỹ0-9]', '', txt)
    if "QUATANGKHONGBAN" in clean_txt:
        return True
    
    return False

def build_shop_filter(shop_ids):
    unique_ids = list(set(shop_ids))
    if not unique_ids:
        return "1=0"
    ids_str = ", ".join(str(x) for x in unique_ids)
    return f"shop_id IN ({ids_str})"

def build_item_filter(shop_item_pairs):
    grouped = defaultdict(list)
    for (s, i) in shop_item_pairs:
        grouped[s].append(i)
    clauses = []
    for s, i_list in grouped.items():
        i_list = list(set(i_list))
        i_str = ", ".join(str(x) for x in i_list)
        clauses.append(f"(shop_id = {s} AND item_id IN ({i_str}))")
    if not clauses:
        return "1=0"
    return " OR ".join(clauses)

def safe_gsheet_value(val):
    """
    Đảm bảo mọi giá trị đều convert được sang JSON (string, number).
    - None => ""
    - datetime/date => str ("YYYY-MM-DD" hoặc "YYYY-MM-DD HH:MM:SS")
    - float/int => giữ nguyên
    - khác => ép str
    """
    if val is None:
        return ""
    if isinstance(val, (pd.Timestamp, datetime)):
        return val.strftime("%Y-%m-%d %H:%M:%S")
    if isinstance(val, date):
        return val.isoformat()  # "YYYY-MM-DD"
    if isinstance(val, (float, int)):
        return val
    return str(val)

# ------------------------------------------------------------------------------
# SHOP-LEVEL MODE
# ------------------------------------------------------------------------------
def run_shop_level_mode(gsheet_manager, ss, ws, shop_col_letter=None, shop_l30d_col_letter=None, shop_wishlist_col_letter=None, date_str=None):
    """Shop-level mode using GSheetManager"""
    # Nếu không có tham số thì hỏi từ terminal
    if shop_col_letter is None:
        shop_col_letter = input("Cột chữ chứa shop_id (vd: B): ").strip()
    
    if shop_l30d_col_letter is None:
        print("")
        shop_l30d_col_letter = input("(Shop level) Cột chữ ghi sum L30d GMV (vd: X): ").strip()
    
    if shop_wishlist_col_letter is None:
        shop_wishlist_col_letter = input("(Shop level) Cột chữ ghi wishlist item (vd: Y): ").strip()

    shop_col_idx = col_letter_to_index(shop_col_letter)
    sum_l30d_idx = col_letter_to_index(shop_l30d_col_letter)
    wishlist_idx = col_letter_to_index(shop_wishlist_col_letter)

    rows = ws.get_all_values()
    total_rows = len(rows)
    if total_rows < 2:
        logging.error("Sheet không đủ dữ liệu (>=2 lines).")
        return

    # Ghi header row1 cho 2 cột
    sum_letter = index_to_col_letter(sum_l30d_idx)
    wishlist_letter = index_to_col_letter(wishlist_idx)
    hdr_updates = [
        {
            "range": f"{sum_letter}1",
            "values": [["Last 30D GMV"]]
        },
        {
            "range": f"{wishlist_letter}1",
            "values": [["WISHLIST"]]
        }
    ]
    try:
        ws.batch_update(hdr_updates)
    except Exception as e:
        logging.warning(f"Không ghi được header Shop-level: {e}")

    # Gom shop_id
    shop_ids = []
    for row_num in range(1, total_rows):  # row_num=1 => dòng 2
        row_vals = rows[row_num]
        if len(row_vals) < shop_col_idx:
            continue
        val_shop = row_vals[shop_col_idx - 1].strip()
        if val_shop.isdigit():
            shop_ids.append(int(val_shop))

    if not shop_ids:
        logging.error("Không tìm thấy shop_id hợp lệ.")
        return

    unique_shops = list(set(shop_ids))

    # Prompt ngày nếu chưa có
    if date_str is None:
        date_str = input("Nhập ngày chứa data (YYYY-MM-DD): ").strip()
    
    table_suffix = parse_scraped_date(date_str)
    table_name = f"`beyondk-live-data.scraped_data.{table_suffix}`"

    where_clause = build_shop_filter(unique_shops)

    # Use bigquery client from GSheetManager
    bq_client = gsheet_manager.bigquery
    if not bq_client:
        logging.error("Không thể kết nối BigQuery. Vui lòng kiểm tra xác thực.")
        return

    # Query
    query_sl = f"""
    SELECT
      shop_id,
      item_id,
      ANY_VALUE(item_name) AS item_name,
      ANY_VALUE(L30d_gmv) AS L30d_gmv
    FROM {table_name}
    WHERE {where_clause}
    GROUP BY shop_id, item_id
    """
    try:
        df_shop = bq_client.query(query_sl).to_dataframe()
    except Exception as e:
        logging.error(f"Lỗi query shop-level: {e}")
        return

    if df_shop.empty:
        logging.warning("Không có dữ liệu shop-level.")
        return

    # Exclude quà
    df_shop['item_name'] = df_shop['item_name'].fillna("")
    df_shop['L30d_gmv'] = pd.to_numeric(df_shop['L30d_gmv'], errors='coerce').fillna(0)
    df_filtered = df_shop[~df_shop['item_name'].apply(is_excluded_gift)].copy()

    # Group => sum => top3
    g_shop = df_filtered.groupby('shop_id')
    shop_dict = {}
    for sid, grp in g_shop:
        sum_l30d = grp['L30d_gmv'].sum()
        grp_sorted = grp.sort_values('L30d_gmv', ascending=False)
        top3 = grp_sorted.head(3)

        wl_lines = []
        for _, rowv in top3.iterrows():
            iname = rowv['item_name'].strip()
            wl_lines.append(f"- {iname}")
        wl_text = "\n".join(wl_lines)

        shop_dict[sid] = {
            "sum_l30d": sum_l30d,
            "wishlist": wl_text
        }

    # Ghi vào sheet
    updates_data = []
    row_updated_count = 0
    for row_num in range(1, total_rows):
        row_vals = rows[row_num]
        if len(row_vals) < shop_col_idx:
            continue
        val_shop = row_vals[shop_col_idx - 1].strip()
        if not val_shop.isdigit():
            continue
        sid = int(val_shop)

        data_found = shop_dict.get(sid)
        if not data_found:
            continue

        sum_val = data_found["sum_l30d"]
        wl_val = data_found["wishlist"]

        sum_range = f"{sum_letter}{row_num+1}"
        updates_data.append({
            "range": sum_range,
            "values": [[safe_gsheet_value(sum_val)]]
        })

        wl_range = f"{wishlist_letter}{row_num+1}"
        updates_data.append({
            "range": wl_range,
            "values": [[wl_val]]
        })

        row_updated_count += 1

    if updates_data:
        try:
            ws.batch_update(updates_data, value_input_option="USER_ENTERED")
            logging.info(f"Shop-level: Đã cập nhật {row_updated_count} dòng.")
        except Exception as e:
            logging.error(f"Lỗi batch_update shop-level: {e}")
    else:
        logging.info("Không có dòng nào cần cập nhật shop-level.")

# ------------------------------------------------------------------------------
# ITEM-LEVEL MODE
# ------------------------------------------------------------------------------
def run_item_level_mode(gsheet_manager, ss, ws, item_col_letter=None, shop_col_letter=None, 
                        start_scraped_col_letter=None, link_col_letter=None, date_str=None):
    """Item-level mode using GSheetManager"""
    # Nếu không có tham số thì hỏi từ terminal
    if item_col_letter is None:
        item_col_letter = input("Cột chữ chứa item_id (vd: B): ").strip()
    
    if shop_col_letter is None:
        shop_col_letter = input("Cột chữ chứa shop_id (vd: F): ").strip()
    
    if start_scraped_col_letter is None:
        print("")
        start_scraped_col_letter = input("(Item level) Cột chữ để bắt đầu ghi dữ liệu scraped (vd: Z): ").strip()
    
    if link_col_letter is None:
        link_col_letter = input("(Item level) Cột chữ ghi dữ liệu item link (vd: AA): ").strip()

    item_col_idx = col_letter_to_index(item_col_letter)
    shop_col_idx = col_letter_to_index(shop_col_letter)
    start_idx = col_letter_to_index(start_scraped_col_letter)
    link_idx = col_letter_to_index(link_col_letter)

    rows = ws.get_all_values()
    total_rows = len(rows)
    if total_rows < 2:
        logging.error("Sheet không đủ dữ liệu.")
        return

    # Ghi header row1, row2 cho 5 cột (4 cột + link)
    # row1 => "BKdata"
    # row2 => ["30D NMV", "Lifetime NMV", "Created date", "Current price", "Link hình"]
    item_headers = ["30D NMV", "Lifetime NMV", "Created date", "Current price"]
    # 4 cột chính => start_idx -> start_idx+3
    updates_header = []

    for offset, hdr_text in enumerate(item_headers):
        col_i = start_idx + offset
        col_letter = index_to_col_letter(col_i)
        # row 1 => "BKdata"
        updates_header.append({
            "range": f"{col_letter}1",
            "values": [["BKdata"]]
        })
        # row 2 => hdr_text
        updates_header.append({
            "range": f"{col_letter}2",
            "values": [[hdr_text]]
        })

    # Cột link => row1 => "BKdata", row2 => "Item link"
    link_col_letter_u = index_to_col_letter(link_idx)
    updates_header.append({
        "range": f"{link_col_letter_u}1",
        "values": [["BKdata"]]
    })
    updates_header.append({
        "range": f"{link_col_letter_u}2",
        "values": [["Link hình"]]
    })

    try:
        ws.batch_update(updates_header)
    except Exception as e:
        logging.warning(f"Không ghi được header Item-level: {e}")

    # Prompt ngày nếu chưa có
    if date_str is None:
        date_str = input("Nhập ngày chứa data (YYYY-MM-DD): ").strip()
    
    table_suffix = parse_scraped_date(date_str)
    table_name = f"`beyondk-live-data.scraped_data.{table_suffix}`"

    # Gom (shop_id, item_id)
    original_pairs = []
    for row_num in range(1, total_rows):
        row_vals = rows[row_num]
        if len(row_vals) < max(item_col_idx, shop_col_idx):
            continue
        val_item = row_vals[item_col_idx - 1].strip()
        val_shop = row_vals[shop_col_idx - 1].strip()
        if val_item.isdigit() and val_shop.isdigit():
            original_pairs.append((int(val_shop), int(val_item)))

    if not original_pairs:
        logging.error("Không có (shop_id, item_id) hợp lệ ở sheet.")
        return

    unique_pairs = list(set(original_pairs))

    # Bước 1: Query central_ids => cpuid
    center_clause = []
    for (sid, iid) in unique_pairs:
        center_clause.append(f"(shop_id = {sid} AND item_id = {iid})")
    if not center_clause:
        logging.error("Không có cặp shop-item => dừng.")
        return

    center_where = " OR ".join(center_clause)
    query_center = f"""
    SELECT shop_id, item_id, canonical_product_uid
    FROM `beyondk-live-data.central.central_ids`
    WHERE {center_where}
    """
    try:
        df_center = gsheet_manager.bigquery.query(query_center).to_dataframe()
    except Exception as e:
        logging.error(f"Lỗi query central_ids (item-level): {e}")
        return

    if df_center.empty:
        logging.error("Không tìm thấy UID cho (shop_id, item_id) input.")
        return

    orig_to_cuid = {}
    for _, rowv in df_center.iterrows():
        s = int(rowv["shop_id"])
        i = int(rowv["item_id"])
        cp = rowv["canonical_product_uid"]
        orig_to_cuid[(s, i)] = cp

    # Bước 2: Lấy variation
    all_uids = list(set(orig_to_cuid.values()))
    var_clauses = [f"canonical_product_uid = '{cp}'" for cp in all_uids]
    var_where = " OR ".join(var_clauses)
    query_var = f"""
    SELECT shop_id, item_id, canonical_product_uid
    FROM `beyondk-live-data.central.central_ids`
    WHERE {var_where}
    """
    try:
        df_var = gsheet_manager.bigquery.query(query_var).to_dataframe()
    except Exception as e:
        logging.error(f"Lỗi query variation: {e}")
        return

    if df_var.empty:
        logging.error("Không thấy variation nào.")
        return

    # dict => cpuid => list of (shop_id, item_id)
    variation_map = defaultdict(list)
    for _, rowv in df_var.iterrows():
        cpid = rowv["canonical_product_uid"]
        s2 = int(rowv["shop_id"])
        i2 = int(rowv["item_id"])
        variation_map[cpid].append((s2, i2))

    # Bước 3: Query scraped_data => all variation
    all_var_pairs = []
    for cp, pairs in variation_map.items():
        all_var_pairs.extend(pairs)
    all_var_pairs = list(set(all_var_pairs))

    where_filter = build_item_filter(all_var_pairs)
    query_scraped = f"""
    SELECT
      shop_id,
      item_id,
      ANY_VALUE(item_name) AS item_name,
      ANY_VALUE(L30d_gmv) AS L30d_gmv,
      ANY_VALUE(lifetime_gmv) AS lifetime_gmv,
      ANY_VALUE(created_date) AS created_date,
      ANY_VALUE(current_price) AS current_price,
      ANY_VALUE(image_link) AS image_link
    FROM {table_name}
    WHERE {where_filter}
    GROUP BY shop_id, item_id
    """
    try:
        df_scraped_all = gsheet_manager.bigquery.query(query_scraped).to_dataframe()
    except Exception as e:
        logging.error(f"Lỗi query {table_name}: {e}")
        return

    if df_scraped_all.empty:
        logging.warning("scraped_data không có data cho variation.")
        return

    # Chuyển numeric
    df_scraped_all['L30d_gmv'] = pd.to_numeric(df_scraped_all['L30d_gmv'], errors='coerce').fillna(0.0)
    df_scraped_all['lifetime_gmv'] = pd.to_numeric(df_scraped_all['lifetime_gmv'], errors='coerce').fillna(0.0)

    # Bước 4: Xác định cpuid cho mỗi row
    rev_map = {}
    for cp, p_list in variation_map.items():
        for (s3, i3) in p_list:
            rev_map[(s3, i3)] = cp

    df_scraped_all["cpuid"] = df_scraped_all.apply(lambda r: rev_map.get((r["shop_id"], r["item_id"])), axis=1)
    df_scraped_all.dropna(subset=["cpuid"], inplace=True)

    # Group theo (cpuid, shop_id) => pick row L30d_gmv max
    def pick_max_l30d(grp):
        max_idx = grp["L30d_gmv"].idxmax()
        return grp.loc[max_idx]

    grouped = df_scraped_all.groupby(["cpuid", "shop_id"], as_index=False, group_keys=False)
    try:
        df_max = grouped.apply(pick_max_l30d, include_groups=False).reset_index(drop=True)
    except TypeError:
        # Nếu pandas < 2.0, param include_groups chưa hỗ trợ => gọi không param
        df_max = grouped.apply(pick_max_l30d).reset_index(drop=True)

    # Tạo dict => (cpuid, shop_id) => data row
    csh_dict = {}
    for _, rowv in df_max.iterrows():
        cp = rowv["cpuid"]
        s_ = int(rowv["shop_id"])
        csh_dict[(cp, s_)] = {
            "L30d_gmv": rowv["L30d_gmv"],
            "lifetime_gmv": rowv["lifetime_gmv"],
            "created_date": rowv["created_date"],
            "current_price": rowv["current_price"],
            "image_link": rowv["image_link"]
        }

    # Bước 5: Map về (shop_id, item_id) gốc
    final_dict = {}
    for (s0, i0), cp0 in orig_to_cuid.items():
        key = (cp0, s0)
        if key in csh_dict:
            final_dict[(s0, i0)] = csh_dict[key]

    # Bước 6: Ghi ra sheet
    updates_data = []
    row_updated_count = 0
    for row_num in range(1, total_rows):
        row_vals = rows[row_num]
        if len(row_vals) < max(item_col_idx, shop_col_idx):
            continue
        val_item = row_vals[item_col_idx - 1].strip()
        val_shop = row_vals[shop_col_idx - 1].strip()
        if not (val_item.isdigit() and val_shop.isdigit()):
            continue

        s_ = int(val_shop)
        i_ = int(val_item)
        data_found = final_dict.get((s_, i_))
        if not data_found:
            continue

        l30d_ = data_found["L30d_gmv"]
        life_ = data_found["lifetime_gmv"]
        created_ = data_found["created_date"]
        curr_ = data_found["current_price"]
        link_ = data_found["image_link"]

        row_data = [l30d_, life_, created_, curr_]
        for offset, valx in enumerate(row_data):
            col_idx = start_idx + offset
            col_letter = index_to_col_letter(col_idx)
            cell_range = f"{col_letter}{row_num+1}"
            updates_data.append({
                "range": cell_range,
                "values": [[safe_gsheet_value(valx)]]
            })

        # link
        link_letter = index_to_col_letter(link_idx)
        link_cell = f"{link_letter}{row_num+1}"
        updates_data.append({
            "range": link_cell,
            "values": [[safe_gsheet_value(link_)]]
        })

        row_updated_count += 1

    if updates_data:
        try:
            ws.batch_update(updates_data, value_input_option="USER_ENTERED")
            logging.info(f"Item-level: Đã cập nhật {row_updated_count} dòng (max L30d variation).")
        except Exception as e:
            logging.error(f"Lỗi batch_update item-level: {e}")
    else:
        logging.info("Không có dòng item-level nào cần cập nhật.")

# ------------------------------------------------------------------------------
def main():
    print("=== CHỌN CHẾ ĐỘ TRUY XUẤT ===")
    print("1) Shop-level (loại quà tặng, sum L30d, top 3 items)")
    print("2) Item-level (gom variation, pick row max L30d, KHÔNG loại quà)")
    choice = input("Lựa chọn (1/2): ").strip()

    sheet_link = input("Google Sheet URL: ").strip()
    worksheet_name = input("Worksheet name (tab): ").strip()

    # Initialize GSheetManager
    gsheet_manager = GSheetManager()
    
    # Open sheet using GSheetManager
    sheet, error = gsheet_manager.open_sheet(sheet_link)
    if error:
        logging.error(f"Lỗi kết nối Google Sheet: {error}")
        return
        
    try:
        ws = sheet.worksheet(worksheet_name)
    except Exception as e:
        logging.error(f"Lỗi khi mở worksheet '{worksheet_name}': {str(e)}")
        return

    if choice == "1":
        run_shop_level_mode(gsheet_manager, sheet, ws)
    elif choice == "2":
        run_item_level_mode(gsheet_manager, sheet, ws)
    else:
        logging.error("Lựa chọn không hợp lệ. Dừng.")


if __name__ == "__main__":
    main()
