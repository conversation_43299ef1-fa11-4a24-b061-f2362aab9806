from PyQt6.QtCore import QThread, pyqtSignal

class ExportWorker(QThread):
    progress = pyqtSignal(str)  # For logging
    finished = pyqtSignal(bool, str)  # (success, error_message)
    
    def __init__(self, export_function, args):
        super().__init__()
        self.export_function = export_function
        self.args = args
        self._is_running = False
        
    def run(self):
        self._is_running = True
        try:
            # Pass bq_client explicitly
            self.export_function(self.args, self.progress.emit)
            if self._is_running:  # Check if cancelled
                self.finished.emit(True, "")
        except Exception as e:
            if self._is_running:  # Check if cancelled
                self.finished.emit(False, str(e))
        finally:
            self._is_running = False
            
    def stop(self):
        """Safely stop the worker"""
        self._is_running = False
