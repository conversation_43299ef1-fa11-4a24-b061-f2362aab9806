import sys
import os
import io  # Thêm import io cho việc xử lý stdout/stderr

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                           QFormLayout, QLabel, QLineEdit, QPushButton, 
                           QComboBox, QMessageBox, QTextEdit, QRadioButton, 
                           QButtonGroup, QTabWidget, QScrollArea, QSizePolicy, QProgressBar)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from gsheet_manager import GSheetManager
import pandas as pd
import logging
from datetime import datetime, timedelta 
from dateutil.relativedelta import relativedelta
from collections import defaultdict
import re
import subprocess

# Đảm bảo tiếng Việt hiển thị đúng trong console
if sys.stdout is not None:
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
if sys.stderr is not None:
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# Hide console windows on Windows
if sys.platform.startswith('win'):
    # This will hide console windows created by subprocess
    startupinfo = subprocess.STARTUPINFO()
    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
    startupinfo.wShowWindow = 0  # SW_HIDE
    
    # Patch subprocess.Popen to use these settings by default
    original_popen = subprocess.Popen
    def popen_no_window(*args, **kwargs):
        if 'startupinfo' not in kwargs:
            kwargs['startupinfo'] = startupinfo
        return original_popen(*args, **kwargs)
    subprocess.Popen = popen_no_window
    
    # Không chuyển hướng stderr sang devnull nữa
    # Thay vào đó, đã đặt UTF-8 encoding ở trên

# Thêm hàm safe_text để xử lý ký tự tiếng Việt trong môi trường .exe
def safe_text(text):
    """
    Chuyển đổi ký tự tiếng Việt thành ASCII an toàn
    khi không chắc chắn về môi trường encoding
    """
    vietnamese_chars = {
        'à': 'a', 'á': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
        'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
        'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
        'è': 'e', 'é': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
        'ê': 'e', 'ề': 'e', 'ế': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
        'ì': 'i', 'í': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
        'ò': 'o', 'ó': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
        'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
        'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
        'ù': 'u', 'ú': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
        'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
        'ỳ': 'y', 'ý': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
        'đ': 'd', 'Đ': 'D'
    }
    
    result = ''
    for char in text:
        result += vietnamese_chars.get(char.lower(), char)
    
    return result

# Import all required functions from export_item6 at module level
from export_item6 import (
    col_letter_to_index,
    format_number,
    build_filter,
    parse_sales_table_date,
    filter_tables_for_completed_months,
    query_gmv_for_tables,
    compute_gmv_aggregations,
    PriceAPI,
    parse_price_campaign_info
)

class ExportWorker(QThread):
    """Worker thread for export operations"""
    progress = pyqtSignal(str)  # Progress message
    progress_value = pyqtSignal(int)  # Progress bar value (0-100)
    progress_max = pyqtSignal(int)  # Set maximum value for progress bar
    
    def __init__(self, export_widget, args):
        super().__init__()
        self.export_widget = export_widget
        self.args = args
        self.bq_client = None

    def run(self):
        try:
            # Set initial progress bar configuration - indeterminate mode
            self.progress_max.emit(0)  # 0 max = indeterminate mode
            
            # Initialize BigQuery client
            self.bq_client = self.export_widget.gsheet_manager.bigquery
            if not self.bq_client:
                raise ValueError("Không thể khởi tạo kết nối BigQuery")

            # Use original executeExport logic but with progress signals
            self.emit_progress("Khởi tạo kết nối Google Sheet...")
            sheet, error = self.export_widget.gsheet_manager.open_sheet(self.args['sheet_url'])
            if error:
                raise ValueError(error)

            ws = sheet.worksheet(self.args['worksheet_name'])
            
            self.emit_progress("Đang đọc dữ liệu sheet...")
            rows = ws.get_all_values()
            if len(rows) < 2:
                raise ValueError("Không tìm thấy dữ liệu trong sheet")
            
            # After we know how many rows to process, set determinate progress mode
            total_steps = 7  # Major processing steps
            self.progress_max.emit(total_steps)
            self.progress_value.emit(1)  # 1/7 steps complete

            self.emit_progress("Đang xử lý shop và item ID...")
            
            # Use the actual executeExport code but replace log() calls with progress.emit()
            # and update progress bar at major steps
            self.export_widget.executeExport(self.args, self.bq_client, self)
            
            self.emit_progress("Export completed successfully!")
            self.progress_value.emit(total_steps)
            
        except Exception as e:
            self.emit_progress(f"Error: {str(e)}")
            raise
            
        finally:
            if self.bq_client:
                self.bq_client.close()
                
    def emit_progress(self, message):
        """Emit progress message with safe text handling for frozen environments"""
        if getattr(sys, 'frozen', False):
            self.progress.emit(safe_text(message))
        else:
            self.progress.emit(message)

class LoadSheetWorker(QThread):
    """Worker thread for loading sheets"""
    finished = pyqtSignal(object, str)  # (sheet_object, error_message)
    progress = pyqtSignal(str)
    
    def __init__(self, gsheet_manager, sheet_url):
        super().__init__()
        self.gsheet_manager = gsheet_manager
        self.sheet_url = sheet_url
        
    def run(self):
        try:
            self.emit_progress("Đang kết nối Google Sheet...")
            QThread.msleep(100)  # Allow UI to update
            
            sheet, error = self.gsheet_manager.open_sheet(self.sheet_url)
            if error:
                self.finished.emit(None, error)
                return
                
            self.emit_progress("Đang tải worksheets...")
            QThread.msleep(100)  # Allow UI to update
            
            # Pre-fetch worksheet data in background
            worksheets_data = []
            worksheet_names = []
            for ws in sheet.worksheets():
                worksheet_names.append(ws.title)
                # Pre-load header rows to avoid blocking later
                if ws.title.lower() == "deal list":
                    _ = ws.row_values(2)  # Load mapping row
                else:
                    _ = ws.row_values(1)  # Load first row
                QThread.msleep(50)  # Small delay between worksheet loads
                
            self.finished.emit((sheet, worksheet_names), "")
            
        except Exception as e:
            self.finished.emit(None, str(e))
            
    def emit_progress(self, message):
        """Emit progress message with safe text handling for frozen environments"""
        if getattr(sys, 'frozen', False):
            self.progress.emit(safe_text(message))
        else:
            self.progress.emit(message)

class ExportItem6Widget(QWidget):  # Giữ nguyên tên class để không ảnh hưởng đến main.py
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Export Item UI")
        self.setMinimumWidth(600)  # Reduced width
        self.setMaximumHeight(650)  # Reduced height
        
        # Add GSheetManager instance
        self.gsheet_manager = GSheetManager()
        
        # Initialize gmv_columns before setupUI
        self.gmv_columns = {
            'all': {},
            '6m': {},
            '3m': {}
        }
        
        self.setupUI()
        self._bq_client = None  # Add this line
        self.export_worker = None
        self.loading_dialog = None
        self.is_loading = False

    def setupUI(self):
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # Create tab widget
        tab_widget = QTabWidget()

        # Tab 1: Input Settings
        input_tab = QWidget()
        input_layout = QVBoxLayout(input_tab)
        
        # Sheet Info Group (including KOL name)
        sheet_group = QGroupBox("Sheet Info")
        sheet_layout = QVBoxLayout()
        sheet_layout.setSpacing(5)
        
        # Sheet URL and Load button in one line
        url_layout = QHBoxLayout()
        self.sheetIdEdit = QLineEdit()
        self.loadSheetBtn = QPushButton("Load")
        self.loadSheetBtn.setMaximumWidth(60)
        url_layout.addWidget(self.sheetIdEdit)
        url_layout.addWidget(self.loadSheetBtn)
        
        self.worksheetCombo = QComboBox()
        self.kolNameEdit = QLineEdit()  # Moved from Basic Settings
        
        sheet_layout.addLayout(url_layout)
        sheet_layout.addWidget(QLabel("Select Worksheet:"))
        sheet_layout.addWidget(self.worksheetCombo)
        sheet_layout.addWidget(QLabel("KOL Name:"))
        sheet_layout.addWidget(self.kolNameEdit)
        sheet_group.setLayout(sheet_layout)
        
        # Basic Settings Group
        basic_group = QGroupBox("Basic Settings")
        basic_layout = QFormLayout()
        basic_layout.setSpacing(5)
        self.itemColEdit = QLineEdit()
        self.shopColEdit = QLineEdit()
        self.origPriceColEdit = QLineEdit()  # Moved from Price group
        
        basic_layout.addRow("Item ID Column:", self.itemColEdit)
        basic_layout.addRow("Shop ID Column:", self.shopColEdit)
        basic_layout.addRow("Original Price Column:", self.origPriceColEdit)
        basic_group.setLayout(basic_layout)
        
        input_layout.addWidget(sheet_group)
        input_layout.addWidget(basic_group)
        input_layout.addStretch()
        
        # Tab 2: Output Settings
        output_tab = QWidget()
        output_layout = QVBoxLayout(output_tab)
        
        # Create scroll area
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setFrameShape(scroll.Shape.NoFrame)
        
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        
        # Create grid layout for A-B-C-D sections
        grid_layout = QHBoxLayout()
        
        # Left column (A and C)
        left_column = QVBoxLayout()
        
        # Section A: Price Output
        price_group = QGroupBox("Price Information")
        price_layout = QFormLayout()
        price_layout.setSpacing(5)
        
        self.lowestPriceColEdit = QLineEdit()
        self.priceCampaignColEdit = QLineEdit()
        
        price_layout.addRow("Lowest Price Column:", self.lowestPriceColEdit)
        price_layout.addRow("Campaign Info Column:", self.priceCampaignColEdit)
        price_group.setLayout(price_layout)
        
        # Section C: 6 Months GMV
        gmv_6m_group = QGroupBox("6 Months GMV")
        gmv_6m_layout = QFormLayout()
        
        self.gmv_columns['6m'] = {
            'max': QLineEdit(),
            'max_info': QLineEdit(),
            'avg': QLineEdit(),
            'min': QLineEdit(),
            'min_info': QLineEdit()
        }
        
        gmv_6m_layout.addRow("Max GMV Column:", self.gmv_columns['6m']['max'])
        gmv_6m_layout.addRow("Max GMV Info Column:", self.gmv_columns['6m']['max_info'])
        gmv_6m_layout.addRow("Average GMV Column:", self.gmv_columns['6m']['avg'])
        gmv_6m_layout.addRow("Min GMV Column:", self.gmv_columns['6m']['min'])
        gmv_6m_layout.addRow("Min GMV Info Column:", self.gmv_columns['6m']['min_info'])
        gmv_6m_group.setLayout(gmv_6m_layout)
        
        left_column.addWidget(price_group)
        left_column.addWidget(gmv_6m_group)
        
        # Right column (B and D)
        right_column = QVBoxLayout()
        
        # Section B: All Time GMV
        gmv_all_group = QGroupBox("All Time GMV")
        gmv_all_layout = QFormLayout()
        
        self.gmv_columns['all'] = {
            'max': QLineEdit(),
            'max_info': QLineEdit(),
            'avg': QLineEdit(),
            'min': QLineEdit(),
            'min_info': QLineEdit()
        }
        
        gmv_all_layout.addRow("Max GMV Column:", self.gmv_columns['all']['max'])
        gmv_all_layout.addRow("Max GMV Info Column:", self.gmv_columns['all']['max_info'])
        gmv_all_layout.addRow("Average GMV Column:", self.gmv_columns['all']['avg'])
        gmv_all_layout.addRow("Min GMV Column:", self.gmv_columns['all']['min'])
        gmv_all_layout.addRow("Min GMV Info Column:", self.gmv_columns['all']['min_info'])
        gmv_all_group.setLayout(gmv_all_layout)
        
        # Section D: 3 Months GMV
        gmv_3m_group = QGroupBox("3 Months GMV")
        gmv_3m_layout = QFormLayout()
        
        self.gmv_columns['3m'] = {
            'max': QLineEdit(),
            'max_info': QLineEdit(),
            'avg': QLineEdit(),
            'min': QLineEdit(),
            'min_info': QLineEdit()
        }
        
        gmv_3m_layout.addRow("Max GMV Column:", self.gmv_columns['3m']['max'])
        gmv_3m_layout.addRow("Max GMV Info Column:", self.gmv_columns['3m']['max_info'])
        gmv_3m_layout.addRow("Average GMV Column:", self.gmv_columns['3m']['avg'])
        gmv_3m_layout.addRow("Min GMV Column:", self.gmv_columns['3m']['min'])
        gmv_3m_layout.addRow("Min GMV Info Column:", self.gmv_columns['3m']['min_info'])
        gmv_3m_group.setLayout(gmv_3m_layout)
        
        right_column.addWidget(gmv_all_group)
        right_column.addWidget(gmv_3m_group)
        
        # Add columns to grid
        grid_layout.addLayout(left_column)
        grid_layout.addLayout(right_column)
        
        scroll_layout.addLayout(grid_layout)
        scroll.setWidget(scroll_content)
        output_layout.addWidget(scroll)
        
        # Add tabs
        tab_widget.addTab(input_tab, "Input")
        tab_widget.addTab(output_tab, "Output")
        
        main_layout.addWidget(tab_widget)
        
        # Run button and status - Modify this section
        run_layout = QHBoxLayout()
        run_layout.setSpacing(10)  # Add spacing between button and status
        
        self.runButton = QPushButton("Start Export")
        self.runButton.setMinimumHeight(35)  # Make button taller
        self.runButton.setStyleSheet("""
            QPushButton {
                background-color: #0d6efd;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        
        # Set size policies to make button expand horizontally
        self.runButton.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        
        run_layout.addWidget(self.runButton)
        
        main_layout.addLayout(run_layout)
        
        # Add Progress Bar - Replace the existing progress bar code with this minimal line style
        progress_layout = QVBoxLayout()
        progress_layout.setContentsMargins(0, 5, 0, 5)
        
        # Create a container for progress bar and percentage label
        progress_container = QWidget()
        progress_container_layout = QHBoxLayout(progress_container)
        progress_container_layout.setContentsMargins(0, 0, 0, 0)
        progress_container_layout.setSpacing(10)
        
        # Create minimal line progress bar
        self.progressBar = QProgressBar()
        self.progressBar.setRange(0, 100)
        self.progressBar.setValue(0)
        self.progressBar.setTextVisible(False)  # Hide default text
        self.progressBar.setFixedHeight(4)  # Make it thin
        self.progressBar.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        
        # Apply minimal line style
        self.progressBar.setStyleSheet("""
            QProgressBar {
                border: none;
                background-color: #e0e0e0;
                border-radius: 2px;
            }
            QProgressBar::chunk {
                background-color: #0078d4;
                border-radius: 2px;
            }
        """)
        
        # Create percentage label
        self.percentLabel = QLabel("0%")
        self.percentLabel.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        self.percentLabel.setFixedWidth(40)  # Fixed width for the percentage
        self.percentLabel.setStyleSheet("""
            color: #555555;
            font-weight: bold;
        """)
        
        # Add progress bar and label to container
        progress_container_layout.addWidget(self.progressBar)
        progress_container_layout.addWidget(self.percentLabel)
        
        # Add container to main layout
        progress_layout.addWidget(progress_container)
        main_layout.addLayout(progress_layout)
        
        # Log area (compact)
        self.logText = QTextEdit()
        self.logText.setReadOnly(True)
        self.logText.setMaximumHeight(100)
        main_layout.addWidget(self.logText)
        
        # Connect signals
        self.loadSheetBtn.clicked.connect(self.loadSheets)
        self.runButton.clicked.connect(self.runExport)
        self.worksheetCombo.currentIndexChanged.connect(self.onWorksheetSelected)

    def log(self, message):
        """Add message to log with timestamp"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # Khi chạy file .exe (frozen), sử dụng safe_text để tránh lỗi encoding
        if getattr(sys, 'frozen', False):
            message = safe_text(message)
            
        self.logText.append(f"[{timestamp}] {message}")
        self.logText.verticalScrollBar().setValue(
            self.logText.verticalScrollBar().maximum()
        )

    def loadSheets(self):
        """Load sheets and auto-detect Deal list"""
        try:
            if self.is_loading:
                return
                
            sheet_url = self.sheetIdEdit.text().strip()
            if not sheet_url:
                raise ValueError("Vui lòng nhập URL Google Sheet")
            
            # Set loading state
            self.is_loading = True
            
            # Disable UI elements
            self._disableUIElements()
            
            # Show loading indicator in log area instead of dialog
            self.log("Đang kết nối Google Sheet... Vui lòng chờ...")
            
            # Create and start worker
            self.load_worker = LoadSheetWorker(self.gsheet_manager, sheet_url)
            self.load_worker.progress.connect(self.log)
            self.load_worker.finished.connect(self._handleLoadSheetFinished)
            
            # Start worker
            self.load_worker.start()
            
        except Exception as e:
            self._handleError("Sheet Loading Error", str(e))

    def _updateLoadingMessage(self, message):
        """Update loading status in log area"""
        self.log(message)

    def _handleLoadSheetFinished(self, result, error):
        """Handle when sheet loading finishes"""
        try:
            # First make sure we're no longer loading
            self.is_loading = False
            
            # Always re-enable UI elements
            self._enableUIElements()
            
            # Only continue processing if there's no error
            if error:
                raise ValueError(error)
                
            sheet, worksheet_names = result
            
            # Process worksheet data
            self.worksheetCombo.clear()
            deal_list_index = -1
            
            for idx, ws_name in enumerate(worksheet_names):
                self.worksheetCombo.addItem(ws_name)
                if ws_name.lower() == "deal list":
                    deal_list_index = idx
                    
            # Auto select Deal list if exists
            if deal_list_index >= 0:
                self.worksheetCombo.setCurrentIndex(deal_list_index)
                self.loadWorksheetData(sheet.worksheet("Deal list"))
            
            self.log("Đã tải danh sách worksheet thành công")
            
        except Exception as e:
            self._handleError("Sheet Loading Error", str(e))

    def _handleError(self, title, message):
        """Handle errors consistently"""
        # First reset the loading state
        self.is_loading = False
        
        # Log the error
        self.log(f"Lỗi: {message}")
        
        # Re-enable UI elements
        self._enableUIElements()
        
        # Show error message
        QMessageBox.warning(self, title, str(message))

    def _disableUIElements(self):
        """Disable UI elements during loading"""
        self.loadSheetBtn.setEnabled(False)
        self.runButton.setEnabled(False)
        self.worksheetCombo.setEnabled(False)
        self.sheetIdEdit.setEnabled(False)

    def _enableUIElements(self):
        """Re-enable UI elements after loading"""
        self.loadSheetBtn.setEnabled(True)
        self.runButton.setEnabled(True)
        self.worksheetCombo.setEnabled(True)
        self.sheetIdEdit.setEnabled(True)

    def runExport(self):
        try:
            self.runButton.setEnabled(False)
            self.log("Starting export process...")
            
            # Reset progress bar
            self.progressBar.setValue(0)
            
            # Use BigQuery client from gsheet_manager instead of creating a new client
            self._bq_client = self.gsheet_manager.bigquery
            
            # Validate and get input values
            args = self.getExportArgs()
            
            # Create and start worker thread
            self.export_worker = ExportWorker(self, args)
            self.export_worker.progress.connect(self.log)
            self.export_worker.progress_value.connect(self.updateProgressValue)
            self.export_worker.progress_max.connect(self.updateProgressMax)
            self.export_worker.finished.connect(self.enableRunButton)
            self.export_worker.start()
            
        except Exception as e:
            self.log(f"Error setting up export: {str(e)}")
            self.runButton.setEnabled(True)

    def updateProgressValue(self, value):
        """Update progress bar value and percentage text"""
        self.progressBar.setValue(value)
        # Also update the percentage label
        if self.progressBar.maximum() > 0:
            percent = int((value / self.progressBar.maximum()) * 100)
            self.percentLabel.setText(f"{percent}%")
        else:
            # For indeterminate mode
            self.percentLabel.setText("")
        
    def updateProgressMax(self, max_value):
        """Update progress bar maximum"""
        if max_value == 0:
            # Indeterminate mode
            self.progressBar.setRange(0, 0)
            self.percentLabel.setText("...")  # Show ellipsis in indeterminate mode
        else:
            # Determinate mode (0-100%)
            self.progressBar.setRange(0, max_value)
            percent = int((self.progressBar.value() / max_value) * 100)
            self.percentLabel.setText(f"{percent}%")

    def enableRunButton(self):
        """Re-enable run button when export finishes"""
        self.runButton.setEnabled(True)
        self.export_worker = None
        
        # Reset progress bar to 100% when done
        if self.progressBar.maximum() > 0:
            self.progressBar.setValue(self.progressBar.maximum())
            self.percentLabel.setText("100%")

    def getExportArgs(self):
        """Collect and validate all export arguments"""
        sheet_url = self.sheetIdEdit.text().strip()
        worksheet_name = self.worksheetCombo.currentText()
        
        # Validate required fields
        if not sheet_url:
            raise ValueError("Google Sheet URL is required")
        if not worksheet_name:
            raise ValueError("Worksheet name is required")
            
        # Validate column formats
        def validate_column(col, name):
            if col and not re.match(r'^[A-Za-z]+$', col):
                raise ValueError(f"Invalid {name} column format. Use letters only (e.g., A, B, AA)")
        
        validate_column(self.itemColEdit.text(), "Item ID")
        validate_column(self.shopColEdit.text(), "Shop ID")
        validate_column(self.origPriceColEdit.text(), "Original Price")
        validate_column(self.lowestPriceColEdit.text(), "Lowest Price")
        validate_column(self.priceCampaignColEdit.text(), "Price Campaign Info")
        
        # Sửa lại cách đặt tên cho rõ ràng và nhất quán
        args = {
            'sheet_url': sheet_url,
            'worksheet_name': worksheet_name,
            'item_col': self.itemColEdit.text(),
            'shop_col': self.shopColEdit.text(),
            'kol_name': self.kolNameEdit.text(),
            
            # GMV columns - All time
            'max_col': self.gmv_columns['all']['max'].text(),
            'max_info_col': self.gmv_columns['all']['max_info'].text(),
            'avg_col': self.gmv_columns['all']['avg'].text(), 
            'min_col': self.gmv_columns['all']['min'].text(),
            'min_info_col': self.gmv_columns['all']['min_info'].text(),
            
            # GMV columns - 6 months 
            'max_6m_col': self.gmv_columns['6m']['max'].text(),
            'max_6m_info_col': self.gmv_columns['6m']['max_info'].text(),
            'avg_6m_col': self.gmv_columns['6m']['avg'].text(),
            'min_6m_col': self.gmv_columns['6m']['min'].text(),
            'min_6m_info_col': self.gmv_columns['6m']['min_info'].text(),
            
            # GMV columns - 3 months
            'max_3m_col': self.gmv_columns['3m']['max'].text(),
            'max_3m_info_col': self.gmv_columns['3m']['max_info'].text(), 
            'avg_3m_col': self.gmv_columns['3m']['avg'].text(),
            'min_3m_col': self.gmv_columns['3m']['min'].text(),
            'min_3m_info_col': self.gmv_columns['3m']['min_info'].text(),
            
            # Price columns
            'lowest_price_col': self.lowestPriceColEdit.text(),
            'price_campaign_info_col': self.priceCampaignColEdit.text(),
            'orig_price_col': self.origPriceColEdit.text()
        }

        return args

    def executeExport(self, args, bq_client, worker=None):
        try:
            # Use BigQuery client from gsheet_manager
            if not self._bq_client:
                self._bq_client = self.gsheet_manager.bigquery
                if not self._bq_client:
                    raise ValueError("Could not initialize BigQuery client. Please check credentials.")

            # Thêm mã kiểm tra xem client có active không
            try:
                # Thực hiện một truy vấn đơn giản để kiểm tra kết nối
                _ = self._bq_client.query("SELECT 1").result()
            except Exception as auth_error:
                # Nếu lỗi xác thực, thử tải lại client
                self.log("Phát hiện lỗi xác thực BigQuery, đang thử kết nối lại...")
                self._bq_client = self.gsheet_manager.refresh_bigquery_client()
                if not self._bq_client:
                    raise ValueError("Không thể kết nối lại BigQuery. Vui lòng khởi động lại ứng dụng và xác thực lại.")
                # Cập nhật lại client được truyền vào hàm
                bq_client = self._bq_client

            # Rest of the export logic
            self.log("Khởi tạo kết nối Google Sheet...")
            sheet, error = self.gsheet_manager.open_sheet(args['sheet_url'])
            if error:
                raise ValueError(error)

            ws = sheet.worksheet(args['worksheet_name'])
            
            # Lấy thông tin về kích thước của worksheet
            try:
                sheet_metadata = ws.spreadsheet.fetch_sheet_metadata()
                sheet_id = None
                for s in sheet_metadata['sheets']:
                    if s['properties']['title'] == args['worksheet_name']:
                        sheet_id = s['properties']['sheetId']
                        max_rows = s['properties']['gridProperties']['rowCount']
                        max_columns = s['properties']['gridProperties']['columnCount']
                        break
                
                if not sheet_id:
                    self.log("Không thể xác định kích thước sheet, tiếp tục với thận trọng")
                    max_rows = float('inf')
                    max_columns = float('inf')
                else:
                    self.log(f"Kích thước sheet: {max_rows} dòng × {max_columns} cột")
            except Exception as e:
                self.log(f"Lỗi khi lấy kích thước sheet: {e}. Tiếp tục với thận trọng.")
                max_rows = float('inf')
                max_columns = float('inf')
            
            # Helper function to check if a cell is within sheet limits
            def is_within_sheet_limits(col_letter, row_num):
                col_idx = col_letter_to_index(col_letter)
                return col_idx <= max_columns and row_num <= max_rows
            
            self.log("Đang đọc dữ liệu sheet...")
            rows = ws.get_all_values()
            if len(rows) < 2:
                raise ValueError("Không tìm thấy dữ liệu trong sheet")

            item_col_idx = col_letter_to_index(args['item_col'])
            shop_col_idx = col_letter_to_index(args['shop_col'])

            # Xác định offset cho header
            row_offset = 2  # Tăng từ 1 lên 2 để bổ sung dòng trống thứ 3
            
            # Make sure we don't exceed sheet limits when adding the offset
            if len(rows) + row_offset > max_rows:
                self.log(f"Điều chỉnh offset dòng để phù hợp với giới hạn sheet ({max_rows} dòng)")
                row_offset = 0  # If we can't fit with offset, don't use offset

            # 1. Gather shop_id, item_id pairs
            self.log("Đang xử lý shop và item ID...")
            sheet_pairs = []
            
            # Skip header row by starting from index 2 
            header_row = 2  # Thay đổi từ 1 lên 2 để bỏ qua thêm dòng trống
            # If worksheet is "Deal list", skip 2 rows
            if args['worksheet_name'].lower() == "deal list":
                header_row = 3  # Thay đổi từ 2 lên 3 để bỏ qua thêm dòng trống
            
            # Thêm log để debug
            self.log(f"Số dòng tổng cộng: {len(rows)}, Bắt đầu từ dòng: {header_row+1}")
            self.log(f"Cột item ID: {args['item_col']} (index {item_col_idx}), Cột shop ID: {args['shop_col']} (index {shop_col_idx})")
            
            # Kiểm tra và tự động điều chỉnh cột nếu cần thiết
            item_col_adjusted = False
            shop_col_adjusted = False
            
            # Thử tự động dò tìm vị trí cột ID nếu không tìm thấy dữ liệu
            if len(rows) > header_row:
                # Kiểm tra 5 dòng đầu tiên để tìm dữ liệu
                sample_rows = rows[header_row:min(header_row+5, len(rows))]
                
                # Kiểm tra xem cột item_id có dữ liệu không
                item_ids_found = False
                for row in sample_rows:
                    if item_col_idx <= len(row) and row[item_col_idx - 1].strip():
                        # Thử xử lý chuỗi để xem có phải số không
                        val_item = re.sub(r'[^\d]', '', row[item_col_idx - 1].strip())
                        if val_item and val_item.isdigit():
                            item_ids_found = True
                            break
                
                # Thử quét để tìm cột chứa ID nếu không tìm thấy trong cột đã chỉ định
                if not item_ids_found:
                    self.log(f"Không tìm thấy dữ liệu item ID ở cột {args['item_col']}. Đang quét tìm tự động...")
                    for col_idx in range(min(15, len(sample_rows[0]) if sample_rows else 0)):  # Scan first 15 columns
                        ids_found = 0
                        for row in sample_rows:
                            if col_idx < len(row) and row[col_idx].strip():
                                val = re.sub(r'[^\d]', '', row[col_idx].strip())
                                if val and val.isdigit() and len(val) > 5:  # item IDs thường dài
                                    ids_found += 1
                        
                        if ids_found >= len(sample_rows) // 2:  # Nếu tìm thấy ID trong ít nhất nửa số dòng
                            new_col_letter = chr(65 + col_idx) if col_idx < 26 else chr(64 + col_idx//26) + chr(65 + col_idx%26)
                            self.log(f"Phát hiện có khả năng cột item ID ở vị trí {new_col_letter} (index {col_idx+1})")
                            item_col_idx = col_idx + 1
                            item_col_adjusted = True
                            break
                
                # Tương tự cho shop_id
                shop_ids_found = False
                for row in sample_rows:
                    if shop_col_idx <= len(row) and row[shop_col_idx - 1].strip():
                        val_shop = re.sub(r'[^\d]', '', row[shop_col_idx - 1].strip())
                        if val_shop and val_shop.isdigit():
                            shop_ids_found = True
                            break
                
                if not shop_ids_found:
                    self.log(f"Không tìm thấy dữ liệu shop ID ở cột {args['shop_col']}. Đang quét tìm tự động...")
                    for col_idx in range(min(15, len(sample_rows[0]) if sample_rows else 0)):
                        ids_found = 0
                        for row in sample_rows:
                            if col_idx < len(row) and row[col_idx].strip():
                                val = re.sub(r'[^\d]', '', row[col_idx].strip())
                                if val and val.isdigit() and len(val) <= 10:  # shop ID thường ngắn hơn item ID
                                    ids_found += 1
                        
                        if ids_found >= len(sample_rows) // 2:
                            # Đảm bảo không trùng với item_col_idx đã điều chỉnh
                            if col_idx + 1 != item_col_idx or not item_col_adjusted:
                                new_col_letter = chr(65 + col_idx) if col_idx < 26 else chr(64 + col_idx//26) + chr(65 + col_idx%26)
                                self.log(f"Phát hiện có khả năng cột shop ID ở vị trí {new_col_letter} (index {col_idx+1})")
                                shop_col_idx = col_idx + 1
                                shop_col_adjusted = True
                                break
            
            if item_col_adjusted or shop_col_adjusted:
                self.log(f"Đã điều chỉnh: Cột item ID mới: index {item_col_idx}, Cột shop ID mới: index {shop_col_idx}")
            
            valid_row_count = 0
            invalid_row_count = 0
            error_rows = []
            
            for row_idx, row in enumerate(rows[header_row:], start=header_row+1):
                try:
                    if len(row) >= max(item_col_idx, shop_col_idx):
                        val_item = row[item_col_idx - 1].strip() if item_col_idx <= len(row) else ""
                        val_shop = row[shop_col_idx - 1].strip() if shop_col_idx <= len(row) else ""
                        
                        # Hiển thị một số dòng đầu tiên để debug
                        if row_idx <= header_row + 3:
                            self.log(f"Dòng {row_idx} - Dữ liệu gốc: Item={val_item}, Shop={val_shop}")
                        
                        # Xử lý trường hợp có dấu phẩy ngăn cách hàng nghìn hoặc các ký tự đặc biệt khác
                        original_item = val_item
                        original_shop = val_shop
                        
                        val_item = re.sub(r'[^\d]', '', val_item)
                        val_shop = re.sub(r'[^\d]', '', val_shop)
                        
                        # Hiển thị dữ liệu sau khi xử lý cho các dòng đầu
                        if row_idx <= header_row + 3:
                            self.log(f"Dòng {row_idx} - Sau xử lý: Item={val_item}, Shop={val_shop}")
                        
                        if val_item and val_shop:  # Kiểm tra không rỗng trước
                            try:
                                sheet_pairs.append((int(val_shop), int(val_item)))
                                valid_row_count += 1
                            except ValueError as ve:
                                invalid_row_count += 1
                                if len(error_rows) < 5:
                                    error_rows.append(f"Dòng {row_idx}: Lỗi chuyển đổi số - Shop={val_shop}, Item={val_item}")
                        else:
                            invalid_row_count += 1
                            if len(error_rows) < 5:
                                reason = []
                                if not val_item:
                                    reason.append(f"Item rỗng [{original_item}]")
                                if not val_shop:
                                    reason.append(f"Shop rỗng [{original_shop}]")
                                error_rows.append(f"Dòng {row_idx}: {', '.join(reason)}")
                    else:
                        invalid_row_count += 1
                except Exception as e:
                    invalid_row_count += 1
                    if len(error_rows) < 5:
                        error_rows.append(f"Dòng {row_idx}: Lỗi {str(e)}")

            # Log kết quả để debug
            self.log(f"Tìm thấy {valid_row_count} dòng hợp lệ, {invalid_row_count} dòng không hợp lệ")
            if error_rows:
                self.log(f"Một số dòng không hợp lệ: {', '.join(error_rows)}")
            
            # Hiển thị danh sách các cặp shop_id, item_id đầu tiên để debug
            if sheet_pairs:
                self.log(f"5 cặp (shop_id, item_id) đầu tiên: {sheet_pairs[:5]}")

            if not sheet_pairs:
                self.log("CẢNH BÁO NGHIÊM TRỌNG: Không tìm thấy cặp shop_id và item_id hợp lệ nào!")
                raise ValueError(f"Không tìm thấy cặp shop_id, item_id hợp lệ. Vui lòng kiểm tra các cột {args['shop_col']} và {args['item_col']}, và đảm bảo dữ liệu là số nguyên.")

            # Update progress at key points if worker is provided
            if worker:
                worker.progress_value.emit(2)  # 2/7 steps complete

            # 2. Get canonical_product_uid
            self.log("Đang truy xuất canonical product ID...")
            unique_pairs = list(set(sheet_pairs))
            center_clauses = [f"(shop_id = {sid} AND item_id = {iid})" 
                            for sid, iid in unique_pairs]
            
            query_center = f"""
            SELECT shop_id, item_id, canonical_product_uid
            FROM `beyondk-live-data.central.central_ids`
            WHERE {" OR ".join(center_clauses)}
            """
            
            center_df = bq_client.query(query_center).to_dataframe()
            if center_df.empty:
                raise ValueError("No matching records found in central_ids")

            # Build mapping
            orig_to_canonical = {
                (int(row["shop_id"]), int(row["item_id"])): row["canonical_product_uid"]
                for _, row in center_df.iterrows()
            }

            if worker:
                worker.progress_value.emit(3)  # 3/7 steps complete

            # 3. Get item variations
            self.log("Đang truy xuất biến thể sản phẩm...")
            shop_cpid_set = set(orig_to_canonical.values())
            var_clauses = [f"canonical_product_uid = '{cpuid}'" 
                          for cpuid in shop_cpid_set]
            
            query_var = f"""
            SELECT shop_id, canonical_product_uid, item_id
            FROM `beyondk-live-data.central.central_ids`
            WHERE {" OR ".join(var_clauses)}
            """
            
            var_df = bq_client.query(query_var).to_dataframe()
            if var_df.empty:
                raise ValueError("No variations found in central_ids")

            # Build variation map
            variation_map = defaultdict(list)
            for _, row in var_df.iterrows():
                sid = int(row["shop_id"])
                cpuid = row["canonical_product_uid"]
                iid = int(row["item_id"])
                variation_map[(sid, cpuid)].append(iid)

            if worker:
                worker.progress_value.emit(4)  # 4/7 steps complete

            # 4. Get and filter Sales_data tables
            self.log("Đang truy xuất bảng Sales_data...")
            kol_name = args['kol_name'].strip()
            if kol_name and kol_name.lower() != 'all':
                tbl_query = f"""
                SELECT table_name
                FROM `beyondk-live-data.Sales_data.INFORMATION_SCHEMA.TABLES`
                WHERE LOWER(table_name) LIKE '%_{kol_name.lower()}'
                """
            else:
                tbl_query = """
                SELECT table_name
                FROM `beyondk-live-data.Sales_data.INFORMATION_SCHEMA.TABLES`
                """
            
            tbl_df = bq_client.query(tbl_query).to_dataframe()
            if tbl_df.empty:
                raise ValueError("No matching Sales_data tables found")

            table_list_all = tbl_df["table_name"].tolist()
            
            # Always get data for all periods
            table_list_6m = filter_tables_for_completed_months(table_list_all, 6) 
            table_list_3m = filter_tables_for_completed_months(table_list_all, 3)

            # 5. Query GMV data
            self.log("Đang truy xuất dữ liệu GMV...")
            df_gmv_all = query_gmv_for_tables(bq_client, variation_map, table_list_all)
            df_gmv_6m = query_gmv_for_tables(bq_client, variation_map, table_list_6m)
            df_gmv_3m = query_gmv_for_tables(bq_client, variation_map, table_list_3m)

            if df_gmv_all.empty and df_gmv_6m.empty and df_gmv_3m.empty:
                self.log("Cảnh báo: Không tìm thấy dữ liệu GMV")
                return

            if worker:
                worker.progress_value.emit(5)  # 5/7 steps complete

            # 6. Compute GMV aggregations
            self.log("Đang tính toán tổng hợp GMV...")
            updates = []

            # Define all GMV configurations
            gmv_configs = [
                # All time GMV
                {
                    'df': df_gmv_all,
                    'columns': {
                        'max': (args.get('max_col'), args.get('max_info_col')),
                        'avg': (args.get('avg_col'), None),
                        'min': (args.get('min_col'), args.get('min_info_col'))
                    },
                    'prefix': ''
                },
                # 6 months GMV
                {
                    'df': df_gmv_6m,
                    'columns': {
                        'max': (args.get('max_6m_col'), args.get('max_6m_info_col')),
                        'avg': (args.get('avg_6m_col'), None),
                        'min': (args.get('min_6m_col'), args.get('min_6m_info_col'))
                    },
                    'prefix': '6 MONTHS '
                },
                # 3 months GMV
                {
                    'df': df_gmv_3m,
                    'columns': {
                        'max': (args.get('max_3m_col'), args.get('max_3m_info_col')),
                        'avg': (args.get('avg_3m_col'), None),
                        'min': (args.get('min_3m_col'), args.get('min_3m_info_col'))
                    },
                    'prefix': '3 MONTHS '
                }
            ]

            # Process each GMV configuration with better logging
            for config in gmv_configs:
                period_name = 'All time' if config['prefix'] == '' else config['prefix'].strip()
                self.log(f"Processing {period_name} GMV data...")
                
                if config['df'] is not None and not config['df'].empty:
                    for agg_type, (col, info_col) in config['columns'].items():
                        if not col:  # Skip if column not specified
                            continue
                            
                        self.log(f"Computing {agg_type} aggregation...")
                        df_agg = compute_gmv_aggregations(config['df'], variation_map, agg_type)
                        if df_agg.empty:
                            self.log(f"No {agg_type} GMV data found")
                            continue

                        # Add "BKdata" header in row 1, header in row 2, empty row 3
                        header = f"{config['prefix']}{agg_type.upper()} GMV"
                        updates.extend([
                            {"range": f"{col}1", "values": [["BKdata"]]},
                            {"range": f"{col}2", "values": [[header]]},
                            {"range": f"{col}3", "values": [[""]]},  # Dòng trống thứ 3
                        ])
                        
                        # Store columns to be formatted later
                        columns_to_format_as_currency = []
                        if col:
                            columns_to_format_as_currency.append(col)
                        
                        if info_col:
                            updates.extend([
                                {"range": f"{info_col}1", "values": [["BKdata"]]},
                                {"range": f"{info_col}2", "values": [[f"{header} Info"]]},
                                {"range": f"{info_col}3", "values": [[""]]},  # Dòng trống thứ 3
                            ])

                        # Start updating data from row 4
                        update_count = 0
                        for row_num, row_values in enumerate(rows[header_row:], start=header_row+1+row_offset):
                            # Kiểm tra có nằm trong giới hạn sheet không
                            if row_num > max_rows:
                                self.log(f"Dòng {row_num} vượt quá giới hạn sheet ({max_rows}). Bỏ qua.")
                                continue
                                
                            # Kiểm tra cột có nằm trong giới hạn sheet không
                            if not is_within_sheet_limits(col, row_num):
                                continue
                                
                            try:
                                row_item = int(row_values[item_col_idx - 1].strip())
                                row_shop = int(row_values[shop_col_idx - 1].strip())
                                cpid = orig_to_canonical.get((row_shop, row_item))

                                if cpid:
                                    mask = (df_agg['shop_id'] == row_shop) & \
                                          (df_agg['canonical_product_uid'] == cpid)
                                    matching = df_agg[mask]

                                    if not matching.empty:
                                        row = matching.iloc[0]
                                        gmv_value = row['item_gmv']
                                        
                                        # Lưu giá trị số thay vì chuyển thành chuỗi
                                        if gmv_value is not None and not pd.isna(gmv_value):
                                            updates.append({
                                                "range": f"{col}{row_num}",
                                                "values": [[float(gmv_value)]]
                                            })
                                            update_count += 1

                                            if info_col and is_within_sheet_limits(info_col, row_num):
                                                if 'campaign_date' in row and 'kol_name' in row:
                                                    info_text = f"{row['campaign_date']}\n{row['kol_name']}"
                                                    updates.append({
                                                        "range": f"{info_col}{row_num}",
                                                        "values": [[info_text]]
                                                    })
                            except Exception as e:
                                self.log(f"Warning: Error processing GMV row {row_num}: {str(e)}")
                                continue
                                
                        self.log(f"Updated {update_count} rows with {agg_type} GMV data")

            if worker:
                worker.progress_value.emit(6)  # 6/7 steps complete

            # 8. Process price data if needed - Fixed logging and validation
            price_columns_to_format = []
            if args['lowest_price_col'] and args['orig_price_col']:
                self.log("Starting price data processing...")
                
                # Tạo instance PriceAPI từ export_item6
                price_api = PriceAPI()
                
                # Lấy danh sách bảng giá - không cần truyền client trong item6
                price_tables = price_api.get_price_table_list()
                if not price_tables:
                    self.log("Warning: No price tables found")
                    return
                    
                self.log(f"Found {len(price_tables)} price data tables")
                
                # First update headers - dùng định dạng header mới từ item6
                updates.extend([
                    {"range": f"{args['lowest_price_col']}1", "values": [["BKdata"]]},
                    {"range": f"{args['lowest_price_col']}2", "values": [["LOWEST FINAL PRICE"]]},
                    {"range": f"{args['lowest_price_col']}3", "values": [[""]]},
                ])
                
                if args['price_campaign_info_col']:
                    updates.extend([
                        {"range": f"{args['price_campaign_info_col']}1", "values": [["BKdata"]]},
                        {"range": f"{args['price_campaign_info_col']}2", "values": [["Price Campaign Info"]]},
                        {"range": f"{args['price_campaign_info_col']}3", "values": [[""]]},
                    ])

                # Process price data in chunks
                orig_price_col_idx = col_letter_to_index(args['orig_price_col']) - 1
                price_lookup_groups = self._build_price_lookup_groups(
                    rows, orig_price_col_idx, item_col_idx, shop_col_idx,
                    orig_to_canonical, variation_map
                )
                
                if not price_lookup_groups:
                    self.log("No valid price groups found to process")
                    return
                    
                self.log(f"Processing {len(price_lookup_groups)} price lookup groups...")
                price_results = self._process_price_data(
                    price_api, bq_client, price_lookup_groups
                )

                # Add price updates
                price_update_count = 0
                if price_results:
                    # Add lowest price column to format list
                    if args['lowest_price_col']:
                        price_columns_to_format.append(args['lowest_price_col'])
                    
                    for grp, (lowest_price, table_name) in price_results.items():
                        row_nums = price_lookup_groups[grp]['rows']
                        for row_num in row_nums:
                            # Kiểm tra có nằm trong giới hạn sheet không
                            adjusted_row = row_num + row_offset
                            if adjusted_row > max_rows:
                                continue
                                
                            # Kiểm tra cột có nằm trong giới hạn sheet không
                            if is_within_sheet_limits(args['lowest_price_col'], adjusted_row):
                                # Lưu giá trị số thay vì chuyển thành chuỗi
                                updates.append({
                                    "range": f"{args['lowest_price_col']}{adjusted_row}",
                                    "values": [[float(lowest_price)]]
                                })
                                price_update_count += 1
                                
                                # Add campaign info if column specified and table name exists
                                if args['price_campaign_info_col'] and table_name and is_within_sheet_limits(args['price_campaign_info_col'], adjusted_row):
                                    # Sử dụng hàm từ export_item6
                                    campaign_info = parse_price_campaign_info(table_name)
                                    updates.append({
                                        "range": f"{args['price_campaign_info_col']}{adjusted_row}",
                                        "values": [[campaign_info]]
                                    })
                    self.log(f"Added {price_update_count} price updates")
                else:
                    self.log("No price results found to update")

            # Apply all updates with chunking for large updates
            if updates:
                self.log(f"Applying {len(updates)} updates to sheet...")
                try:
                    # Chia batch updates thành các phần nhỏ hơn nếu cần
                    batch_size = 5000  # Giới hạn mỗi batch
                    if len(updates) > batch_size:
                        self.log(f"Chia {len(updates)} cập nhật thành các batch {batch_size}")
                        for i in range(0, len(updates), batch_size):
                            batch = updates[i:i+batch_size]
                            ws.batch_update(batch)
                            self.log(f"Đã xử lý batch {i//batch_size + 1}/{(len(updates)-1)//batch_size + 1}")
                    else:
                        ws.batch_update(updates)
                    self.log("Updates applied successfully!")
                except Exception as e:
                    self.log(f"Error during batch update: {str(e)}")
                    raise Exception(f"Failed to update sheet: {str(e)}")
            else:
                self.log("No updates to apply")
                
            # Áp dụng định dạng số cho các cột sau khi cập nhật
            try:
                # Lấy sheet_id - cần sửa cách truy cập
                try:
                    # Cách 1: Truy cập trực tiếp từ worksheet
                    sheet_id = ws.id
                    self.log(f"Đã lấy sheet ID: {sheet_id}")
                except Exception as e1:
                    self.log(f"Không thể lấy sheet ID từ worksheet trực tiếp: {str(e1)}")
                    try:
                        # Cách 2: Thử sử dụng get_id() nếu có
                        sheet_id = getattr(ws, 'id', None) or getattr(ws, '_properties', {}).get('sheetId')
                        if not sheet_id and hasattr(ws, 'get_id'):
                            sheet_id = ws.get_id()
                        self.log(f"Đã lấy sheet ID qua phương thức thay thế: {sheet_id}")
                    except Exception as e2:
                        self.log(f"Không thể lấy sheet ID: {str(e2)}")
                        sheet_id = None
                
                if not sheet_id:
                    self.log("Không thể tìm thấy sheet ID, bỏ qua định dạng số")
                else:
                    # Tạo danh sách các cột cần định dạng số
                    numeric_columns = []
                    
                    # GMV columns
                    for config in gmv_configs:
                        for _, (col, _) in config['columns'].items():
                            if col and col.strip():
                                numeric_columns.append(col.strip())
                    
                    # Price column
                    if args['lowest_price_col'] and args['lowest_price_col'].strip():
                        numeric_columns.append(args['lowest_price_col'].strip())
                    
                    # Bỏ các cột trùng lặp
                    numeric_columns = list(set(numeric_columns))
                    
                    if numeric_columns:
                        self.log(f"Định dạng số cho các cột: {', '.join(numeric_columns)}")
                        
                        # Phân loại các cột GMV và cột giá
                        gmv_columns = []
                        other_numeric_columns = []
                        
                        # Tìm các cột GMV (không bao gồm info)
                        for config in gmv_configs:
                            for agg_type, (col, info_col) in config['columns'].items():
                                if col and col.strip():
                                    gmv_columns.append(col.strip())
                        
                        # Các cột số còn lại (như giá)
                        for col in numeric_columns:
                            if col not in gmv_columns:
                                other_numeric_columns.append(col)
                        
                        self.log(f"Cột GMV (VND): {', '.join(gmv_columns)}")
                        self.log(f"Cột số khác: {', '.join(other_numeric_columns)}")
                        
                        # Phương pháp thay thế: Sử dụng format riêng lẻ thay vì batch update
                        success_count = 0
                        
                        # 1. Định dạng VNĐ cho các cột GMV
                        for col in gmv_columns:
                            try:
                                col_a1 = f"{col}{header_row + row_offset + 1}:{col}{len(rows) + row_offset}"
                                # Sử dụng định dạng tiền tệ với VND thay vì đ
                                ws.format(col_a1, {
                                    "numberFormat": {
                                        "type": "CURRENCY",
                                        "pattern": "#,##0 \"₫\""
                                    }
                                })
                                success_count += 1
                                self.log(f"Đã áp dụng định dạng tiền tệ VND cho cột {col}")
                            except Exception as col_err:
                                self.log(f"Lỗi khi định dạng cột GMV {col}: {str(col_err)}")
                                continue
                        
                        # 2. Định dạng tiền tệ VND cho cột Lowest Final Price
                        for col in other_numeric_columns:
                            try:
                                col_a1 = f"{col}{header_row + row_offset + 1}:{col}{len(rows) + row_offset}"
                                # Nếu là cột Lowest Final Price, áp dụng định dạng VND
                                if col == args['lowest_price_col']:
                                    ws.format(col_a1, {
                                        "numberFormat": {
                                            "type": "CURRENCY",
                                            "pattern": "#,##0 \"₫\""
                                        }
                                    })
                                    self.log(f"Đã áp dụng định dạng tiền tệ VND cho cột giá {col}")
                                else:
                                    # Các cột số khác vẫn giữ định dạng số thông thường
                                    ws.format(col_a1, {
                                        "numberFormat": {
                                            "type": "NUMBER",
                                            "pattern": "#,##0 \"₫\""
                                        }
                                    })
                                success_count += 1
                            except Exception as col_err:
                                self.log(f"Lỗi khi định dạng cột số {col}: {str(col_err)}")
                                continue
                        
                        self.log(f"Đã áp dụng định dạng cho {success_count}/{len(numeric_columns)} cột")
                    else:
                        self.log("Không có cột nào cần định dạng số")
                        
            except Exception as e:
                self.log(f"Lỗi khi áp dụng định dạng số: {str(e)}")
                # Không raise lỗi ở đây để không làm gián đoạn quá trình
            
            # Final progress update
            if worker:
                worker.progress_value.emit(7)  # 7/7 steps complete

        except Exception as e:
            self.log(f"Export thất bại: {str(e)}")
            raise Exception(f"Export failed: {str(e)}")

    def _build_price_lookup_groups(self, rows, orig_price_col_idx, 
                                 item_col_idx, shop_col_idx,
                                 orig_to_canonical, variation_map):
        """Helper method to build price lookup groups"""
        price_lookup_groups = {}
        
        # Xác định dòng bắt đầu dựa trên tên worksheet - cập nhật để sử dụng 3 dòng header
        start_row = 5 if self.worksheetCombo.currentText().lower() == "deal list" else 3
        
        # Đếm từ dòng bắt đầu, không phải từ dòng 2
        for row_num, row_data in enumerate(rows[start_row-1:], start=start_row):
            try:
                if len(row_data) < max(item_col_idx, shop_col_idx, 
                                     orig_price_col_idx + 1):
                    continue

                row_item = int(row_data[item_col_idx - 1].strip())
                row_shop = int(row_data[shop_col_idx - 1].strip())
                orig_price_str = row_data[orig_price_col_idx].strip()
                cpid = orig_to_canonical.get((row_shop, row_item))

                if not cpid:
                    continue

                import re
                cleaned = re.sub(r'[^0-9.]', '', orig_price_str)
                origin_price = float(cleaned) if cleaned else 0.0

                allowed_ids = variation_map.get((row_shop, cpid), [])
                if not allowed_ids:
                    continue

                allowed_ids_str = ", ".join(str(x) for x in sorted(set(allowed_ids)))
                group_key = (row_shop, cpid, origin_price, allowed_ids_str)
                
                if group_key not in price_lookup_groups:
                    price_lookup_groups[group_key] = {
                        'shop': row_shop,
                        'cpuid': cpid,
                        'origin_price': origin_price,
                        'allowed_ids_str': allowed_ids_str,
                        'rows': []
                    }
                price_lookup_groups[group_key]['rows'].append(row_num)
                
            except Exception as e:
                self.log(f"Warning: Error processing price row {row_num}: {str(e)}")
                continue

        return price_lookup_groups

    def _process_price_data(self, price_api, bq_client, price_lookup_groups):
        """Helper method to process price data"""
        try:
            CHUNK_SIZE = 300
            price_results = {}
            groups = list(price_lookup_groups.values())
            
            # Thêm cơ chế kiểm tra xem client có active không
            try:
                # Thực hiện một truy vấn đơn giản để kiểm tra kết nối
                _ = bq_client.query("SELECT 1").result()
            except Exception as auth_error:
                # Nếu lỗi xác thực, thử tải lại client
                self.log("Phát hiện lỗi xác thực BigQuery, đang thử kết nối lại...")
                bq_client = self.gsheet_manager.refresh_bigquery_client()
                if not bq_client:
                    raise ValueError("Không thể kết nối lại BigQuery. Vui lòng khởi động lại ứng dụng và xác thực lại.")
            
            for start_idx in range(0, len(groups), CHUNK_SIZE):
                chunk = groups[start_idx:start_idx + CHUNK_SIZE]
                conditions = []
                for grp in chunk:
                    cond = (
                        f"(shop_id = {grp['shop']} "
                        f"AND origin_price = {grp['origin_price']} "
                        f"AND ((item_id IN ({grp['allowed_ids_str']})) "
                        f"OR (pitching_item_id IN ({grp['allowed_ids_str']}))))"
                    )
                    conditions.append(cond)
                    
                if not conditions:
                    continue
                    
                combined_condition = " OR ".join(conditions)
                partial_results = []
                
                # Lấy danh sách bảng giá theo cách export_item6
                price_tables = price_api.get_price_table_list()
                if not price_tables:
                    self.log("Không tìm thấy bảng dữ liệu giá. Kiểm tra quyền truy cập BigQuery.")
                    return {}
                    
                for table in price_tables:
                    try:
                        # Sử dụng phương thức từ export_item6
                        df = price_api.get_chunked_price_data(
                            bq_client, combined_condition, table
                        )
                        if not df.empty:
                            partial_results.append(df)
                    except Exception as table_error:
                        self.log(f"Lỗi khi truy vấn bảng {table}: {str(table_error)}")
                        continue  # Bỏ qua bảng lỗi và tiếp tục với bảng khác
                
                if partial_results:
                    price_df = pd.concat(partial_results, ignore_index=True)
                    for grp in chunk:
                        df_filtered = price_df[
                            (price_df['shop_id'] == grp['shop']) &
                            (price_df['origin_price'] == grp['origin_price'])
                        ]
                        if not df_filtered.empty:
                            idx = df_filtered['lowest_price'].idxmin()
                            row = df_filtered.loc[idx]
                            group_key = (grp['shop'], grp['cpuid'],
                                    grp['origin_price'],
                                    grp['allowed_ids_str'])
                            price_results[group_key] = (
                                row['lowest_price'],
                                row['table']
                            )
            return price_results
            
        except Exception as e:
            self.log(f"Lỗi khi xử lý dữ liệu giá: {str(e)}")
            return {}

    def parse_price_campaign_info(self, table_name):
        """
        Given a price_data table name of the form YYYY_MM_DD_KOLname,
        return a string with:
        first line: DD_MM_YY (last two digits of year)
        second line: KOL name
        """
        parts = table_name.split("_")
        if len(parts) >= 4:
            yyyy, mm, dd, kol = parts[0], parts[1], parts[2], parts[3]
            date_str = f"{dd}_{mm}_{yyyy[-2:]}"
            return f"{date_str}\n{kol}"
        else:
            return "N/A"

    def cleanup(self):
        """Cleanup resources"""
        if self.export_worker:
            self.export_worker.terminate()
            self.export_worker.wait()
            self.export_worker = None
            
        if self._bq_client:
            self._bq_client.close()
            self._bq_client = None

    def closeEvent(self, event):
        """Handle widget close"""
        self.cleanup()
        super().closeEvent(event)

    def ensure_columns(self, worksheet, columns_config):
        """
        Đảm bảo tất cả cột cần thiết tồn tại và đúng vị trí
        
        Args:
            worksheet: Worksheet object
            columns_config: List of (col_letter, header) tuples
        """
        try:
            # Lấy toàn bộ dữ liệu hiện tại
            all_values = worksheet.get_all_values()
            if not all_values:
                return False
                
            current_width = len(all_values[0])
            
            # Sắp xếp config theo thứ tự cột để xử lý từ trái qua phải
            columns_to_add = []
            for col_letter, header in columns_config:
                if not col_letter:  # Bỏ qua cột trống
                    continue
                
                col_index = col_letter_to_index(col_letter) - 1  # Convert to 0-indexed
                columns_to_add.append((col_letter, header, col_index))
            
            # Sắp xếp để xử lý từ trái qua phải
            columns_to_add.sort(key=lambda x: x[2])
            
            # Kiểm tra và thêm cột
            for col_letter, header, col_index in columns_to_add:
                if col_index < current_width:
                    # Cột đã tồn tại, chỉ cần giữ nguyên dữ liệu và cập nhật header
                    
                    # 1. Lấy dữ liệu hiện tại của cột
                    range_str = f"{col_letter}4:{col_letter}{len(all_values)}"
                    cell_values = worksheet.get(range_str)
                    values = [cell[0] if cell else "" for cell in cell_values]
                    
                    # 2. Cập nhật lại dữ liệu ở dòng 4 trở đi
                    worksheet.update(f"{col_letter}4:{col_letter}{len(all_values)}", 
                                          [[v] for v in values])
                    
                    # 3. Cập nhật header 3 dòng
                    worksheet.update(f"{col_letter}1:${col_letter}3", [["BKdata"], [header], [""]])
                    
                else:
                    # Thêm cột mới ở vị trí chỉ định
                    
                    # Thêm các cột trống nếu cần
                    empty_cols_needed = col_index - current_width - 1
                    if empty_cols_needed > 0:
                        for _ in range(empty_cols_needed):
                            worksheet.add_column(None)
                            current_width += 1
                    
                    # Thêm cột với header 3 dòng
                    worksheet.add_column([["BKdata"], [header], [""]] + [""] * (len(all_values)-3))
                    current_width += 1
                    
            return True
            
        except Exception as e:
            self.log(f"Lỗi khi đảm bảo cột: {str(e)}")
            return False

    def detectColumns(self):
        """Auto detect columns based on header content"""
        try:
            headers = self.headers
            if not headers:
                return

            # Column mapping definition with exact header names from the sheet
            column_map = {
                'item': {'exact': ['item id']},  # Chỉ khớp chính xác "item id", loại bỏ contains
                'shop': {'exact': ['shop id'], 'contains': ['shopid']},
                'orig_price': {
                    'exact': ['original price', 'origin price'],
                    'contains': ['giá gốc', 'gia goc', 'price original']
                },
                'lowest_price': {
                    'exact': ['lowest final price'],
                    'contains': ['lowest price', 'giá thấp nhất', 'gia thap nhat']
                },
                'campaign_info': {
                    'exact': ['price campaign info'],
                    'contains': ['campaign info', 'thông tin chiến dịch']
                },
                # GMV All Time columns and info
                'max_gmv': {
                    'exact': ['max gmv'],
                    'contains': ['gmv cao nhất']
                },
                'max_gmv_info': {
                    'exact': ['max gmv info'],
                    'contains': ['thông tin gmv cao nhất']
                },
                'avg_gmv': {
                    'exact': ['average gmv', 'avg gmv'],
                    'contains': ['gmv trung bình']
                },
                'min_gmv': {
                    'exact': ['min gmv'],
                    'contains': ['gmv thấp nhất']
                },
                'min_gmv_info': {
                    'exact': ['min gmv info'],
                    'contains': ['thông tin gmv thấp nhất']
                },
                # GMV 6 Months columns - Strict matching to avoid confusion
                'max_6gmv': {
                    'exact': ['6months max gmv', 'max 6m gmv', 'max 6 months gmv', '6 months max gmv', '6m max gmv', '6 month max gmv']
                },
                'max_6gmv_info': {
                    'exact': ['6months max gmv info', 'max 6m gmv info', 'max 6 months gmv info', '6 months max gmv info', '6m max gmv info']
                },
                'avg_6gmv': {
                    'exact': ['6months avg gmv', 'avg 6m gmv', 'average 6m gmv', 'avg 6 months gmv', '6 months avg gmv', '6m avg gmv']
                },
                'min_6gmv': {
                    'exact': ['6months min gmv', 'min 6m gmv', 'min 6 months gmv', '6 months min gmv', '6m min gmv']
                },
                'min_6gmv_info': {
                    'exact': ['6months min gmv info', 'min 6m gmv info', 'min 6 months gmv info', '6 months min gmv info', '6m min gmv info']
                },
                # GMV 3 Months columns - Strict matching to avoid confusion
                'max_3gmv': {
                    'exact': ['3months max gmv', 'max 3m gmv', 'max 3 months gmv', '3 months max gmv', '3m max gmv', '3 month max gmv']
                },
                'max_3gmv_info': {
                    'exact': ['3months max gmv info', 'max 3m gmv info', 'max 3 months gmv info', '3 months max gmv info', '3m max gmv info']
                },
                'avg_3gmv': {
                    'exact': ['3months avg gmv', 'avg 3m gmv', 'average 3m gmv', 'avg 3 months gmv', '3 months avg gmv', '3m avg gmv']
                },
                'min_3gmv': {
                    'exact': ['3months min gmv', 'min 3m gmv', 'min 3 months gmv', '3 months min gmv', '3m min gmv']
                },
                'min_3gmv_info': {
                    'exact': ['3months min gmv info', 'min 3m gmv info', 'min 3 months gmv info', '3 months min gmv info', '3m min gmv info']
                }
            }
            
            # Thêm cả dạng viết hoa cho ITEM ID
            column_map['item']['exact'].append('ITEM ID')

            # Xóa bỏ các contains pattern trong 3m và 6m để tránh nhận nhầm
            for field in column_map:
                if '3gmv' in field or '6gmv' in field:
                    if 'contains' in column_map[field]:
                        del column_map[field]['contains']
                
            # Map field names to UI elements with proper associations for 6M and 3M columns
            field_to_element = {
                'item': self.itemColEdit,
                'shop': self.shopColEdit,
                'orig_price': self.origPriceColEdit,
                'lowest_price': self.lowestPriceColEdit,
                'campaign_info': self.priceCampaignColEdit,
                # All time GMV
                'max_gmv': self.gmv_columns['all']['max'],
                'max_gmv_info': self.gmv_columns['all']['max_info'],
                'avg_gmv': self.gmv_columns['all']['avg'],
                'min_gmv': self.gmv_columns['all']['min'],
                'min_gmv_info': self.gmv_columns['all']['min_info'],
                # 6 months GMV
                'max_6gmv': self.gmv_columns['6m']['max'],
                'max_6gmv_info': self.gmv_columns['6m']['max_info'],
                'avg_6gmv': self.gmv_columns['6m']['avg'],
                'min_6gmv': self.gmv_columns['6m']['min'],
                'min_6gmv_info': self.gmv_columns['6m']['min_info'],
                # 3 months GMV
                'max_3gmv': self.gmv_columns['3m']['max'],
                'max_3gmv_info': self.gmv_columns['3m']['max_info'], 
                'avg_3gmv': self.gmv_columns['3m']['avg'],
                'min_3gmv': self.gmv_columns['3m']['min'],
                'min_3gmv_info': self.gmv_columns['3m']['min_info']
            }
                
            # Track matches to avoid duplicates - important to avoid incorrect mappings
            matched_columns = set()

            # Xử lý đặc biệt cho cột ITEM ID trước tiên
            for col_idx, header in enumerate(headers):
                if not header:
                    continue
                    
                header_text = str(header).strip()  # Không lowercase để giữ nguyên ITEM ID
                col_letter = chr(65 + col_idx) if col_idx < 26 else chr(64 + col_idx//26) + chr(65 + col_idx%26)
                
                # Kiểm tra chính xác ITEM ID (phân biệt chữ hoa/thường)
                if header_text == 'ITEM ID' or header_text == 'item id' or header_text == 'Item ID':
                    field_to_element['item'].setText(col_letter)
                    matched_columns.add(col_letter)
                    break

            # First pass: Check for exact matches only with prioritization for 3m/6m columns
            priority_fields = [field for field in field_to_element.keys() if '3gmv' in field or '6gmv' in field]
            normal_fields = [field for field in field_to_element.keys() if field not in priority_fields and field != 'item']
            
            # First process priority fields (3m/6m columns) with exact matching only
            for col_idx, header in enumerate(headers):
                if not header:
                    continue
                    
                header_text = str(header).lower().strip()
                col_letter = chr(65 + col_idx) if col_idx < 26 else chr(64 + col_idx//26) + chr(65 + col_idx%26)
                
                if col_letter in matched_columns:
                    continue
                    
                for field_id in priority_fields:
                    patterns = column_map[field_id]
                    
                    # Only use exact matches for 3m and 6m columns to avoid confusion
                    if 'exact' in patterns:
                        for pattern in patterns['exact']:
                            if header_text == pattern:
                                field_to_element[field_id].setText(col_letter)
                                matched_columns.add(col_letter)
                                break
                    
                    if col_letter in matched_columns:
                        break
            
            # Then process remaining regular fields with both exact and contains matching
            for col_idx, header in enumerate(headers):
                if not header:
                    continue
                    
                header_text = str(header).lower().strip()
                col_letter = chr(65 + col_idx) if col_idx < 26 else chr(64 + col_idx//26) + chr(65 + col_idx%26)
                
                if col_letter in matched_columns:
                    continue
                    
                for field_id in normal_fields:
                    patterns = column_map[field_id]
                    matched = False
                    
                    # Check exact matches first
                    if 'exact' in patterns:
                        for pattern in patterns['exact']:
                            if header_text == pattern:
                                matched = True
                                break
                    
                    # Then check contains matches if no exact match
                    if not matched and 'contains' in patterns:
                        for pattern in patterns['contains']:
                            if pattern in header_text:
                                matched = True
                                break
                                
                    if matched:
                        field_to_element[field_id].setText(col_letter)
                        matched_columns.add(col_letter)
                        break

        except Exception as e:
            self.log(f"Lỗi khi nhận diện cột: {str(e)}")

    def loadWorksheetData(self, worksheet):
        """Load and process worksheet data"""
        try:
            self.sheet = worksheet
            # For Deal list:
            # Row 1: BKdata
            # Row 2: Column mapping header
            # Row 3: Empty row
            # Row 4+: Data rows
            # For normal sheets:
            # Row 1: BKdata
            # Row 2: Column mapping header
            # Row 3: Empty row
            # Row 4+: Data rows
            self.mapping_row = 2  # Dòng chứa tên cột luôn là dòng 2 
            self.data_start_row = 5 if worksheet.title.lower() == "deal list" else 4  # Dữ liệu bắt đầu từ dòng 4 (hoặc 5 cho Deal list)
            
            # Get mapping headers from row 2 for all sheets
            self.headers = worksheet.row_values(self.mapping_row)
            self.detectColumns()
            self.log("Worksheet data loaded successfully")
            
        except Exception as e:
            error_msg = f"Error loading worksheet data: {str(e)}"
            self.log(error_msg)
            QMessageBox.warning(self, "Error", error_msg)

    def onWorksheetSelected(self, index):
        """Handle worksheet selection changed"""
        if not self.worksheetCombo.count():
            return
            
        try:
            sheet_url = self.sheetIdEdit.text().strip()
            # Use GSheetManager here as well
            sheet, error = self.gsheet_manager.open_sheet(sheet_url)
            if error:
                raise ValueError(error)
                
            worksheet = sheet.get_worksheet(index)
            self.loadWorksheetData(worksheet)
            
        except Exception as e:
            self.log(f"Error changing worksheet: {str(e)}")

    def buildGmvQuery(self, kol_name):
        """Build GMV query based on KOL filter"""
        tables_query = """
            SELECT table_name 
            FROM `beyondk-live-data.Sales_data.INFORMATION_SCHEMA.TABLES`
        """
        if kol_name:
            tables_query += f" WHERE table_name LIKE '%_{kol_name}'"

        return tables_query

    def buildPriceQuery(self, table_name, shop_id, origin_price, item_ids):
        """Build price data query for specific table"""
        item_ids_str = ", ".join(map(str, item_ids))
        return f"""
        SELECT 
            shop_id,
            item_id,
            origin_price,
            MIN(final_price) as lowest_price
        FROM `beyondk-live-data.price_data.{table_name}`
        WHERE shop_id = {shop_id}
        AND origin_price = {origin_price}
        AND (item_id IN ({item_ids_str}) OR pitching_item_id IN ({item_ids_str}))
        GROUP BY shop_id, item_id, origin_price
        """

    def buildVariationQuery(self, shop_ids, item_ids):
        """Build query to get item variations from central_ids"""
        shop_items = [f"(shop_id = {sid} AND item_id = {iid})" 
                     for sid, iid in zip(shop_ids, item_ids)]
        conditions = " OR ".join(shop_items)
        
        return f"""
        WITH original_items AS (
            SELECT canonical_product_uid, shop_id, item_id
            FROM `beyondk-live-data.central.central_ids`
            WHERE {conditions}
        )
        SELECT DISTINCT 
            o.shop_id,
            o.item_id as original_item_id,
            v.item_id as variation_item_id
        FROM original_items o
        JOIN `beyondk-live-data.central.central_ids` v
        ON o.canonical_product_uid = v.canonical_product_uid
        AND o.shop_id = v.shop_id
        """