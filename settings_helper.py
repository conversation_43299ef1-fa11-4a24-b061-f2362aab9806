import os
import json
import logging
from pathlib import Path

class SettingsHelper:
    """Class để quản lý cài đặt ứng dụng"""
    
    def __init__(self):
        """Khởi tạo paths và file settings"""
        # Th<PERSON> mục cài đặt trong APPDATA
        self.app_data_dir = os.path.join(os.environ.get('LOCALAPPDATA', os.path.expanduser('~')), 'BigQueryExport')
        
        # T<PERSON><PERSON> thư mục nếu chưa tồn tại
        if not os.path.exists(self.app_data_dir):
            os.makedirs(self.app_data_dir)
        
        # File cài đặt
        self.settings_path = os.path.join(self.app_data_dir, 'settings.json')
        
        # File logs
        self.logs_path = os.path.join(self.app_data_dir, 'app.log')
        
        # Khởi tạo logging
        logging.basicConfig(
            filename=self.logs_path,
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Load settings hoặc tạo mới nếu chưa có
        self.settings = self._load_settings()
    
    def _load_settings(self):
        """Load cài đặt từ file, hoặc tạo mới nếu không tồn tại"""
        if os.path.exists(self.settings_path):
            try:
                with open(self.settings_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logging.error(f"Không thể đọc file cài đặt: {str(e)}")
                # Trả về cài đặt mặc định nếu file bị lỗi
                return self._get_default_settings()
        else:
            # Tạo file cài đặt mặc định
            default_settings = self._get_default_settings()
            self._save_settings(default_settings)
            return default_settings
    
    def _save_settings(self, settings=None):
        """Lưu cài đặt vào file"""
        if settings is None:
            settings = self.settings
            
        try:
            with open(self.settings_path, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            logging.error(f"Không thể lưu file cài đặt: {str(e)}")
            return False
    
    def _get_default_settings(self):
        """Tạo cài đặt mặc định"""
        return {
            "recent_sheets": [],
            "recent_kols": [],
            "last_selected_sheet": "",
            "last_selected_worksheet": "",
            "ui": {
                "window_position_x": None,
                "window_position_y": None,
                "window_size_width": 700,
                "window_size_height": 700
            }
        }
    
    def save_setting(self, key, value):
        """Lưu một cài đặt cụ thể"""
        # Hỗ trợ key dạng nested với dấu chấm, ví dụ: "ui.window_size_width"
        if '.' in key:
            parts = key.split('.')
            current = self.settings
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
            current[parts[-1]] = value
        else:
            self.settings[key] = value
            
        return self._save_settings()
    
    def get_setting(self, key, default=None):
        """Lấy giá trị cài đặt từ key"""
        try:
            # Hỗ trợ key dạng nested với dấu chấm, ví dụ: "ui.window_size_width"
            if '.' in key:
                parts = key.split('.')
                current = self.settings
                for part in parts:
                    current = current[part]
                return current
            else:
                return self.settings.get(key, default)
        except (KeyError, TypeError):
            return default
    
    def add_recent_sheet(self, sheet_url, max_items=10):
        """Thêm sheet vào danh sách sheet gần đây"""
        recent_sheets = self.get_setting('recent_sheets', [])
        
        # Xóa nếu đã tồn tại để thêm lại vào đầu danh sách
        if sheet_url in recent_sheets:
            recent_sheets.remove(sheet_url)
        
        # Thêm vào đầu danh sách
        recent_sheets.insert(0, sheet_url)
        
        # Giữ chỉ max_items
        recent_sheets = recent_sheets[:max_items]
        
        self.save_setting('recent_sheets', recent_sheets)
    
    def add_recent_kol(self, kol_name, max_items=10):
        """Thêm KOL vào danh sách KOL gần đây"""
        if not kol_name or kol_name.lower() == 'all':
            return
            
        recent_kols = self.get_setting('recent_kols', [])
        
        # Xóa nếu đã tồn tại để thêm lại vào đầu danh sách
        if kol_name in recent_kols:
            recent_kols.remove(kol_name)
        
        # Thêm vào đầu danh sách
        recent_kols.insert(0, kol_name)
        
        # Giữ chỉ max_items
        recent_kols = recent_kols[:max_items]
        
        self.save_setting('recent_kols', recent_kols)
