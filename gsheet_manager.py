import os
import json
import pickle
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from google.cloud import bigquery
import gspread
from typing import Optional, Tuple, Dict
import logging
from google.api_core.exceptions import PermissionDenied
from gspread.exceptions import APIError
from google.oauth2 import credentials as google_credentials
import shutil

class GSheetManager:
    _instance = None
    _credentials = None
    _sheets_client = None 
    _bigquery_client = None
    
    # Tạo thư mục trong LOCAL APPDATA
    _app_data_dir = os.path.join(os.environ.get('LOCALAPPDATA', os.path.expanduser('~')), 'BigQueryExport')
    _token_path = os.path.join(_app_data_dir, 'token.pickle')
    _credentials_path = os.path.join(_app_data_dir, 'credentials.json')
    
    SCOPES = [
        'https://www.googleapis.com/auth/spreadsheets',
        'https://www.googleapis.com/auth/drive',
        'https://www.googleapis.com/auth/bigquery'
    ]

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(GSheetManager, cls).__new__(cls)
            # Đảm bảo thư mục ứng dụng tồn tại
            if not os.path.exists(cls._app_data_dir):
                os.makedirs(cls._app_data_dir)
                
        return cls._instance

    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        self._initialized = True
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def set_credentials_path(self, path: str):
        """Set the path to credentials.json file and copy it to app data directory"""
        if not os.path.exists(path):
            raise FileNotFoundError(f"Credentials file not found at: {path}")
            
        try:
            # Copy credentials.json file to our app data directory
            shutil.copy2(path, self._credentials_path)
            self.logger.info(f"Credentials copied to: {self._credentials_path}")
            
            # Clear existing credentials to force re-authentication
            self._credentials = None
            self._sheets_client = None
            self._bigquery_client = None
            return True
        except Exception as e:
            self.logger.error(f"Error copying credentials file: {e}")
            return False

    def credentials_exist(self) -> bool:
        """Check if credentials.json exists in app data directory"""
        return os.path.exists(self._credentials_path)

    def authenticate(self, force_new: bool = False) -> bool:
        """Authenticate with Google services"""
        try:
            if not self.credentials_exist():
                self.logger.error("Credentials file not found in app data directory")
                return False

            creds = None
            if not force_new and os.path.exists(self._token_path):
                with open(self._token_path, 'rb') as token:
                    creds = pickle.load(token)

            if not creds or not creds.valid or (creds.expired and not creds.refresh_token):
                # Remove quota_project_id from flow creation
                flow = InstalledAppFlow.from_client_secrets_file(
                    self._credentials_path, 
                    self.SCOPES
                )
                creds = flow.run_local_server(port=0)
                
                # Save token
                with open(self._token_path, 'wb') as token:
                    pickle.dump(creds, token)
                    
            elif creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                    with open(self._token_path, 'wb') as token:
                        pickle.dump(creds, token)
                except Exception as e:
                    self.logger.error(f"Token refresh failed: {e}")
                    # Remove quota_project_id here too
                    flow = InstalledAppFlow.from_client_secrets_file(
                        self._credentials_path,
                        self.SCOPES
                    )
                    creds = flow.run_local_server(port=0)
                    with open(self._token_path, 'wb') as token:
                        pickle.dump(creds, token)

            self._credentials = creds
            self._init_clients()
            return True

        except Exception as e:
            self.logger.error(f"Authentication failed: {str(e)}")
            return False

    def _init_clients(self):
        """Initialize Google Sheets and BigQuery clients"""
        try:
            if not self._credentials:
                raise Exception("Credentials not found")
            
            # Ngăn gspread tự động fallback
            os.environ["GSPREAD_CREDENTIALS_FILENAME"] = self._credentials_path
            
            # Tạo client gspread từ credentials thay vì auto/oauth
            self._sheets_client = gspread.authorize(self._credentials)
            
            # Set quota project ID when creating BigQuery client only
            self._bigquery_client = bigquery.Client(
                project='beyondk-live-data',
                credentials=self._credentials
            )
            
            self.logger.info("Clients initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize clients: {str(e)}")
            raise

    @property
    def sheets(self) -> Optional[gspread.Client]:
        """Get Google Sheets client"""
        if not self._sheets_client:
            if not self.authenticate():
                return None
        return self._sheets_client

    @property 
    def bigquery(self) -> Optional[bigquery.Client]:
        """Get BigQuery client"""
        if not self._bigquery_client:
            if not self.authenticate():
                return None
                
            # Convert OAuth2 credentials to google-auth credentials
            google_creds = google_credentials.Credentials(
                token=self._credentials.token,
                refresh_token=self._credentials.refresh_token,
                token_uri=self._credentials.token_uri,
                client_id=self._credentials.client_id,
                client_secret=self._credentials.client_secret,
                scopes=self._credentials.scopes
            )
            
            # Create new BigQuery client with credentials
            self._bigquery_client = bigquery.Client(
                project='beyondk-live-data',
                credentials=google_creds
            )
            
        return self._bigquery_client

    def open_sheet(self, url_or_id: str) -> Tuple[Optional[gspread.Spreadsheet], str]:
        """
        Open a Google Sheet by URL or ID
        Returns:
            Tuple[Spreadsheet, error_message]
        """
        try:
            if not self.sheets:
                return None, "Google Sheets client not initialized"
                
            # Extract ID from URL if needed
            sheet_id = self._extract_sheet_id(url_or_id)
            if not sheet_id:
                return None, "Invalid sheet URL or ID"
                
            spreadsheet = self.sheets.open_by_key(sheet_id)
            return spreadsheet, ""
            
        except Exception as e:
            return None, f"Failed to open sheet: {str(e)}"

    def _extract_sheet_id(self, url_or_id: str) -> Optional[str]:
        """Extract sheet ID from URL or return ID if already in correct format"""
        import re
        # If it's already just an ID (no slashes), return it
        if '/' not in url_or_id:
            return url_or_id
            
        # Try to extract ID from URL
        pattern = r'/spreadsheets/d/([a-zA-Z0-9-_]+)'
        match = re.search(pattern, url_or_id)
        if match:
            return match.group(1)
        return None

    def query_bigquery(self, query: str) -> Tuple[Optional[any], str]:
        """
        Execute a BigQuery query
        Returns:
            Tuple[result, error_message]
        """
        try:
            if not self.bigquery:
                return None, "BigQuery client not initialized"
                
            result = self.bigquery.query(query).to_dataframe()
            return result, ""
            
        except Exception as e:
            return None, f"Query failed: {str(e)}"

    def clear_authentication(self) -> bool:
        """Clear saved authentication token"""
        try:
            if os.path.exists(self._token_path):
                os.remove(self._token_path)
            self._credentials = None
            self._sheets_client = None
            self._bigquery_client = None
            return True
        except Exception as e:
            self.logger.error(f"Failed to clear authentication: {str(e)}")
            return False

    def test_connection(self) -> Tuple[bool, str]:
        """Test connection to both services"""
        if not self.sheets or not self.bigquery:
            return False, "Clients not initialized"
            
        try:
            # Test Sheets API
            _ = self.sheets.list_spreadsheet_files()
            
            # Test BigQuery API
            _ = self.bigquery.query("""
                SELECT 1
            """).result()
            
            return True, "Connection test successful"
            
        except Exception as e:
            return False, f"Connection test failed: {str(e)}"

    def test_permissions(self) -> Dict[str, bool]:
        """
        Test all required API permissions
        Returns:
            Dict with status of each permission
        """
        permissions = {
            'sheets': False,
            'drive': False,
            'bigquery': False
        }
        
        try:
            # Test Sheets API
            if self.sheets:
                _ = self.sheets.list_spreadsheet_files()
                permissions['sheets'] = True
        except APIError as e:
            if 'insufficient permission' in str(e).lower():
                self.logger.warning("Missing Google Sheets permission")
            else:
                self.logger.error(f"Sheets API error: {e}")

        try:
            # Test Drive API through sheets client
            if self.sheets:
                _ = self.sheets.list_spreadsheet_files()
                permissions['drive'] = True
        except APIError as e:
            if 'insufficient permission' in str(e).lower():
                self.logger.warning("Missing Google Drive permission")
            else:
                self.logger.error(f"Drive API error: {e}")

        try:
            # Test BigQuery API
            if self.bigquery:
                _ = self.bigquery.query("SELECT 1").result()
                permissions['bigquery'] = True
        except PermissionDenied:
            self.logger.warning("Missing BigQuery permission")
        except Exception as e:
            self.logger.error(f"BigQuery API error: {e}")

        return permissions

    def get_missing_permissions(self) -> list:
        """Get list of missing API permissions"""
        perms = self.test_permissions()
        return [api for api, has_access in perms.items() if not has_access]

    def check_required_permissions(self) -> Tuple[bool, str]:
        """
        Check if all required permissions are available
        Returns:
            (bool, str): (has_all_permissions, error_message)
        """
        missing = self.get_missing_permissions()
        if not missing:
            return True, ""
            
        error_msg = "Missing required permissions for:\n"
        permission_names = {
            'sheets': 'Google Sheets API',
            'drive': 'Google Drive API',
            'bigquery': 'BigQuery API'
        }
        error_msg += "\n".join(f"- {permission_names[p]}" for p in missing)
        error_msg += "\n\nPlease update permissions in Google Cloud Console"
        return False, error_msg

    def recheck_authentication(self) -> bool:
        """
        Recheck authentication and permissions without requiring new credentials
        Returns:
            bool: True if all permissions now available
        """
        try:
            if not self._credentials_path:
                return False
                
            if self._credentials and self._credentials.expired:
                self._credentials.refresh(Request())
                self._init_clients()
                
            has_all, _ = self.check_required_permissions()
            return has_all
            
        except Exception as e:
            self.logger.error(f"Recheck authentication failed: {e}")
            return False

    def refresh_bigquery_client(self):
        """
        Refresh BigQuery client by re-authenticating and creating a new client.
        This is useful when the token expires or authentication needs refresh.
        
        Returns:
            New BigQuery client or None if failed
        """
        try:
            self.logger.info("Refreshing BigQuery client...")
            
            # First, try to refresh existing credentials
            if self._credentials and self._credentials.refresh_token:
                try:
                    self._credentials.refresh(Request())
                    self.logger.info("Successfully refreshed credentials")
                except Exception as e:
                    self.logger.warning(f"Could not refresh existing credentials: {e}")
                    # Continue with re-authentication
                    
            # If no credentials or refresh failed, try to re-authenticate
            if not self._credentials or not self._credentials.valid:
                if not self._credentials_path:
                    self.logger.error("No credentials path set for re-authentication")
                    return None
                    
                # Force new authentication
                flow = InstalledAppFlow.from_client_secrets_file(
                    self._credentials_path, 
                    self.SCOPES
                )
                self._credentials = flow.run_local_server(port=0)
                
                # Save token
                with open(self._token_path, 'wb') as token:
                    pickle.dump(self._credentials, token)
            
            # Create new BigQuery client
            return self.bigquery
            
        except Exception as e:
            self.logger.error(f"Failed to refresh BigQuery client: {str(e)}")
            return None
